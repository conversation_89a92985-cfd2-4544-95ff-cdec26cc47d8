1. Emoji & Reaction Sentiment Radar
   What: Extract every emoji used in the user’s posts and the posts they liked; build a small radar chart of the top 10 emojis.
   Why: Reveals emotional tone faster than NLP sentiment.
   From logs: we already have text and like_count; emojis are in plain text.
2. Interaction Speed: Measure the time between a post appearing and the user liking it. Consistently sub-second likes are a strong bot signal.

- Value: Provides a data-driven, nuanced assessment of account authenticity, moving beyond simple guesswork.

  2.3 Starter-Pack Penetration
  List starter packs that include the user (app.bsky.graph.getActorStarterPacks) → divide by total starter packs seen.
  “How often am I recommended?”
  2.5 Mute-List Overlaps
  Pull the user’s mute lists (app.bsky.graph.getList) and intersect DIDs with the user’s follows.
  “How many people I follow do I also mute?”
  3.4 Reply Latency Trend
  Linear regression slope of “minutes between parent post and my reply” over last 50 replies. Negative slope → getting faster.
  4.2 Follower-Overlap Heat-Strip
  For every pair of the user’s top-20 most-interacted accounts, compute Jaccard similarity of their followers. Visualised as 20×20 mini heat-strip.
  1.6 Like-to-Reply Ratio on Own Posts
  (# likes on user’s posts) / (# replies to user’s posts).
  High → “broadcast” style, low → “conversation” style. 2. “Ghost-Likers” Detector
  What: Count how many of the user’s likes come from accounts that never reply or repost.
  Why: Quick bot / low-engagement filter beyond the bot-score.
  From logs: every like already includes the liker’s did; we just need to fetch their last N posts.

- "Echo Chamber" Score:
  _ What it shows: A score based on analyzing the user's follows. It answers: Do the people this user follows also follow each other? A high score
  means they are in a tight-knit community.
  _ Value: Helps understand the diversity of the user's information diet.

* Interaction Affinity:
  _ What it shows: A ranked list of accounts the user most frequently likes, replies to, and reposts.
  _ Value: Uncovers the user's primary sources of information and who influences them the most.
