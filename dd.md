1. Emoji & Reaction Sentiment Radar
   What: Extract every emoji used in the user’s posts and the posts they liked; build a small radar chart of the top 10 emojis.
   Why: Reveals emotional tone faster than NLP sentiment.
   From logs: we already have text and like_count; emojis are in plain text.
2. Interaction Speed: Measure the time between a post appearing and the user liking it. Consistently sub-second likes are a strong bot signal.

- Value: Provides a data-driven, nuanced assessment of account authenticity, moving beyond simple guesswork.

  2.3 Starter-Pack Penetration
  List starter packs that include the user (app.bsky.graph.getActorStarterPacks) → divide by total starter packs seen.
  “How often am I recommended?”
  2.5 Mute-List Overlaps
  Pull the user’s mute lists (app.bsky.graph.getList) and intersect DIDs with the user’s follows.
  “How many people I follow do I also mute?”
  3.4 Reply Latency Trend
  Linear regression slope of “minutes between parent post and my reply” over last 50 replies. Negative slope → getting faster.
  4.2 Follower-Overlap Heat-Strip
  For every pair of the user’s top-20 most-interacted accounts, compute Jaccard similarity of their followers. Visualised as 20×20 mini heat-strip.
  1.6 Like-to-Reply Ratio on Own Posts
  (# likes on user’s posts) / (# replies to user’s posts).
  High → “broadcast” style, low → “conversation” style. 2. “Ghost-Likers” Detector
  What: Count how many of the user’s likes come from accounts that never reply or repost.
  Why: Quick bot / low-engagement filter beyond the bot-score.
  From logs: every like already includes the liker’s did; we just need to fetch their last N posts.

- "Echo Chamber" Score:
  _ What it shows: A score based on analyzing the user's follows. It answers: Do the people this user follows also follow each other? A high score
  means they are in a tight-knit community.
  _ Value: Helps understand the diversity of the user's information diet.

* Interaction Affinity:
  _ What it shows: A ranked list of accounts the user most frequently likes, replies to, and reposts.
  _ Value: Uncovers the user's primary sources of information and who influences them the most.

2025-08-26 14:09:45,915 - root - INFO - Thread response: thread=ThreadViewPost(post=PostView(author=ProfileViewBasic(did='did:plc:ckaz32jwl6t2cno6fmuw2nhn', handle='timkellogg.me', associated=ProfileAssociated(activity_subscription=ProfileAssociatedActivitySubscription(allow_subscriptions='followers', py_type='app.bsky.actor.defs#profileAssociatedActivitySubscription'), chat=ProfileAssociatedChat(allow_incoming='all', py_type='app.bsky.actor.defs#profileAssociatedChat'), feedgens=None, labeler=None, lists=None, starter_packs=None, py_type='app.bsky.actor.defs#profileAssociated'), avatar='https://cdn.bsky.app/img/avatar/plain/did:plc:ckaz32jwl6t2cno6fmuw2nhn/bafkreidvgcq72e5erl4stnap6wjzas6a2wburoa7yzctwuy4vgx4vb5fsi@jpeg', created_at='2024-08-13T17:43:08.073Z', display_name='Tim Kellogg', labels=[], status=None, verification=None, viewer=ViewerState(activity_subscription=None, blocked_by=False, blocking=None, blocking_by_list=None, followed_by=None, following='at://did:plc:u3vlkmn4jjgc2o45hfsube74/app.bsky.graph.follow/3lwra42n5jj2m', known_followers=None, muted=False, muted_by_list=None, py_type='app.bsky.actor.defs#viewerState'), py_type='app.bsky.actor.defs#profileViewBasic'), cid='bafyreier5l6uguy4kt7d4f4skn3obf7y7g4ocpbnha6xqj5ccsymv5u3ya', indexed_at='2025-08-25T22:30:21.003Z', record=Record(created_at='2025-08-25T22:30:20.620Z', text='rumors say we should be expecting something from Google this week (Gemini 3??) as well as something from xAI (hentai bot??)', embed=None, entities=None, facets=None, labels=None, langs=['en'], reply=None, tags=None, py_type='app.bsky.feed.post'), uri='at://did:plc:ckaz32jwl6t2cno6fmuw2nhn/app.bskydding_disabled=False, like=None, pinned=None, reply_disabled=None, repost=None, thread_muted=False, py_type='app.bsky.feed.defs#viewerState'), py_type='app.b.feed.post/3lxb2gwuwac2o', embed=None, labels=[], like_count=17, quote_count=0, reply_count=3, repost_count=0, threadgate=None, viewer=ViewerState(embesky.feed.defs#postView'), parent=None, replies=[ThreadViewPost(post=PostView(author=ProfileViewBasic(did='did:plc:yzywgiiou7cx63uddiru6m2o', handle='pekka.bsky.social', associated=ProfileAssociated(activity_subscription=ProfileAssociatedActivitySubscription(allow_subscriptions='followers', py_type='app.bsky.actor.defs#profileAssociatedActivitySubscription'), chat=ProfileAssociatedChat(allow_incoming='following', py_type='app.bsky.actor.defs#profileAssociatedChat'), feedgens=None, labeler=None, lists=None, starter_packs=None, py_type='app.bsky.actor.defs#profileAssociated'), avatar='https://cdn.bsky.app/img/avatar/plain/did:plc:yzywgiiou7cx63uddiru6m2o/bafkreiem6xyw22onibycmnhoj6bex4fnszjcujwah3ipocwe6suoklzspy@jpeg', created_at='2023-07-03T14:02:39.891Z', display_name='Pekka Lund', labels=[], status=None, verification=None, viewer=ViewerState(activity_subscription=None, blocked_by=False, blocking=None, blocking_by_list=None, followed_by=None, following=None, known_followers=None, muted=False, muted_by_list=None, py_type='app.bsky.actor.defs#viewerState'), py_type='app.bsky.actor.defs#profileViewBasic'), cid='bafyreid43dbseejsevhwzncsx3wutsoazkgs4lttgapbe52kxowfjn5u2i', indexed_at='2025-08-25T23:13:57.809Z', record=Record(created_at='2025-08-25T23:13:56.033Z', text='Demis Hassabis has teased nano banana about an hour ago.', embed=None, entities=None, facets=None, labels=None, langs=['en'], reply=ReplyRef(parent=Main(cid='bafyreier5l6uguy4kt7d4f4skn3obf7y7g4ocpbnha6xqj5ccsymv5u3ya', uri='at://did:plc:ckaz32jwl6t2cno6fmuw2nhn/app.bsky.feed.post/3lxb2gwuwac2o', py_type='com.atproto.repo.strongRef'), root=Main(cid='bafyreier5l6uguy4kt7d4f4skn3obf7y7g4ocpbnha6xqj5ccsymv5u3ya', uri='at://did:plc:ckaz32jwl6t2cno6fmuw2nhn/app.bsky.feed.post/3lxb2gwuwac2o', py_type='com.atproto.repo.strongRef'), py_type='app.bsky.feed.post#replyRef'), tags=None, py_type='app.bsky.feed.post'), uri='at://did:plc:yzywgiiou7cx63uddiru6m2o/app.bsky.feed.post/3lxb4uv4xjc2m', embed=None, labels=[], like_count=1, quote_count=0, reply_count=0, repost_count=0, threadgate=None, viewer=ViewerState(embedding_disabled=False, like=None, pinned=None, reply_disabled=None, repost=None, thread_muted=False, py_type='app.bsky.feed.defs#viewerState'), py_type='app.bsky.feed.defs#postView'), parent=None, replies=None, thread_context=ThreadContext(root_author_like=None, py_type='app.bsky.feed.defs#threadContext'), py_type='app.bsky.feed.defs#threadViewPost'), ThreadViewPost(post=PostView(author=ProfileViewBasic(did='did:plc:yzywgiiou7cx63uddiru6m2o', handle='pekka.bsky.social', associated=ProfileAssociated(activity_subscription=ProfileAssociatedActivitySubscription(allow_subscriptions='followers', py_type='app.bsky.actor.defs#profileAssociatedActivitySubscription'), chat=ProfileAssociatedChat(allow_incoming='following', py_type='app.bsky.actor.defs#profileAssociatedChat'), feedgens=None, labeler=None, lists=None, starter_packs=None, py_type='app.bsky.actor.defs#profileAssociated'), avatar='https://cdn.bsky.app/img/avatar/plain/did:plc:yzywgiiou7cx63uddiru6m2o/bafkreiem6xyw22onibycmnhoj6bex4fnszjcujwah3ipocwe6suoklzspy@jpeg', created_at='2023-07-03T14:02:39.891Z', display_name='Pekka Lund', labels=[], status=None, verification=None, viewer=ViewerState(activity_subscription=None, blocked_by=False, blocking=None, blocking_by_list=None, followed_by=None, following=None, known_followers=None, muted=False, muted_by_list=None, py_type='app.bsky.actor.defs#viewerState'), py_type='app.bsky.actor.defs#profileViewBasic'), cid='bafyreibnpdhixfdot3jxhglntbfr46qonbw3shrrxfknsiqe436mbcbyei', indexed_at='2025-08-25T22:32:42.203Z', record=Record(created_at='2025-08-25T22:32:39.873Z', text="Doesn't Google release something every week? And aren't there rumors about imminent Gemini 3 launch every week?", embed=None, entities=None, facets=None, labels=None, langs=['en'], reply=ReplyRef(parent=Main(cid='bafyreier5l6uguy4kt7d4f4skn3obf7y7g4ocpbnha6xqj5ccsymv5u3ya', uri='at://did:plc:ckaz32jwl6t2cno6fmuw2nhn/app.bsky.feed.post/3lxb2gwuwac2o', py_type='com.atproto.repo.strongRef'), root=Main(cid='bafyreier5l6uguy4kt7d4f4skn3obf7y7g4ocpbnha6xqj5ccsymv5u3ya', uri='at://did:plc:ckaz32jwl6t2cno6fmuw2nhn/app.bsky.feed.post/3lxb2gwuwac2o', py_type='com.atproto.repo.strongRef'), py_type='app.bsky.feed.post#replyRef'), tags=None, py_type='app.bsky.feed.post'), uri='at://did:plc:yzywgiiou7cx63uddiru6m2o/app.bsky.feed.post/3lxb2l3ojk22w', embed=None, labels=[], like_count=0, quote_count=0, reply_count=1, repost_count=0, threadgate=None, viewer=ViewerState(embedding_disabled=False, like=None, pinned=None, reply_disabled=None, repost=None, thread_muted=False, py_type='app.bsky.feed.defs#viewerState'), py_type='app.bsky.feed.defs#postView'), parent=None, replies=None, thread_context=ThreadContext(root_author_like=None, py_type='app.bsky.feed.defs#threadContext'), py_type='app.bsky.feed.defs#threadViewPost'), ThreadViewPost(post=PostView(author=ProfileViewBasic(did='did:plc:dn2bsg2krqg77ojjlz5wirtf', handle='rumblestiltskin.bsky.social', associated=ProfileAssociated(activity_subscription=ProfileAssociatedActivitySubscription(allow_subscriptions='followers', py_type='app.bsky.actor.defs#profileAssociatedActivitySubscription'), chat=None, feedgens=None, labeler=None, lists=None, starter_packs=None, py_type='app.bsky.actor.defs#profileAssociated'), avatar='https://cdn.bsky.app/img/avatar/plain/did:plc:dn2bsg2krqg77ojjlz5wirtf/bafkreiftfsyn2pterpxhf37nuli6gd24ftqay5uwdewmgyy2urfhtwh7ie@jpeg', created_at='2023-06-23T18:47:24.262Z', display_name='Rumblestiltskin', labels=[], status=None, verification=None, viewer=ViewerState(activity_subscription=None, blocked_by=False, blocking=None, blocking_by_list=None, followed_by=None, following=None, known_followers=None, muted=False, muted_by_list=None, py_type='app.bsky.actor.defs#viewerState'), py_type='app.bsky.actor.defs#profileViewBasic'), cid='bafyreie2xngiohgitovpw4hoh5bjkg4jhexde2o5qp262lyq2qqzp4elue', indexed_at='2025-08-25T22:39:47.702Z', record=Record(created_at='2025-08-25T22:39:48.323Z', text='I wonder what Hentai is... Let me Google this while sitting on the couch next to my young kids. Lol', embed=None, entities=None, facets=None, labels=None, langs=['en'], reply=ReplyRef(parent=Main(cid='bafyreier5l6uguy4kt7d4f4skn3obf7y7g4ocpbnha6xqj5ccsymv5u3ya', uri='at://did:plc:ckaz32jwl6t2cno6fmuw2nhn/app.bsky.feed.post/3lxb2gwuwac2o', py_type='com.atproto.repo.strongRef'), root=Main(cid='bafyreier5l6uguy4kt7d4f4skn3obf7y7g4ocpbnha6xqj5ccsymv5u3ya', uri='at://did:plc:ckaz32jwl6t2cno6fmuw2nhn/app.bsky.feed.post/3lxb2gwuwac2o', py_type='com.atproto.repo.strongRef'), py_type='app.bsky.feed.post#replyRef'), tags=None, py_type='app.bsky.feed.post'), uri='at://did:plc:dn2bsg2krqg77ojjlz5wirtf/app.bsky.feed.post/3lxb2xubsps2g', embed=None, labels=[], like_count=2, quote_count=0, reply_count=0, repost_count=0, threadgate=None, viewer=ViewerState(embedding_disabled=False, like=None, pinned=None, reply_disabled=None, repost=None, thread_muted=False, py_type='app.bsky.feed.defs#viewerState'), py_type='app.bsky.feed.defs#postView'), parent=None, replies=None, thread_context=ThreadContext(root_author_like='at://did:plc:ckaz32jwl6t2cno6fmuw2nhn/app.bsky.feed.like/3lxb3fxpc342c', py_type='app.bsky.feed.defs#threadContext'), py_type='app.bsky.feed.defs#threadViewPost')], thread_context=ThreadContext(root_author_like=None, py_type='app.bsky.feed.defs#threadContext'), py_type='app.bsky.feed.defs#threadViewPost') threadgate=None
2025-08-26 14:09:45,915 - root - INFO - Found 3 replies
