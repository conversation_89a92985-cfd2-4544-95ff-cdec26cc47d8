import logging
import os
from dotenv import load_dotenv
from atproto import Client

# Load environment variables from .env file
load_dotenv()

logging.basicConfig(
    filename='bluesky_tui.log',
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class BlueskyClient:
    def __init__(self):
        self.client = Client()
        self.profile = None
        self.accounts = {}  # Store multiple account credentials
        self.current_account = None  # Track current active account
        self.load_accounts_from_env()

    def load_accounts_from_env(self):
        """Load multiple accounts from environment variables."""
        try:
            # Load primary account
            handle = os.getenv('BLUESKY_HANDLE')
            password = os.getenv('BLUESKY_PASSWORD')
            if handle and password:
                self.accounts['primary'] = {'handle': handle, 'password': password}
            
            # Load numbered accounts (BLUESKY_HANDLE1, BLUESKY_PASSWORD1, etc.)
            i = 1
            while True:
                handle = os.getenv(f'BLUESKY_HANDLE{i}')
                password = os.getenv(f'BLUESKY_PASSWORD{i}')
                if handle and password:
                    self.accounts[f'account_{i}'] = {'handle': handle, 'password': password}
                    i += 1
                else:
                    break
                    
            logging.info(f"Loaded {len(self.accounts)} accounts from environment")
        except Exception as e:
            logging.error(f"Error loading accounts from environment: {e}")

    def get_account_list(self):
        """Get list of available accounts."""
        return [(key, account['handle']) for key, account in self.accounts.items()]

    def add_account(self, handle, password, account_name=None):
        """Add a new account."""
        if not account_name:
            # Generate account name
            account_name = f"account_{len(self.accounts) + 1}"
        
        self.accounts[account_name] = {'handle': handle, 'password': password}
        logging.info(f"Added account: {handle} as {account_name}")
        return account_name

    def switch_account(self, account_key):
        """Switch to a different account."""
        logging.info(f"Attempting to switch to account key: {account_key}")
        logging.info(f"Available accounts: {list(self.accounts.keys())}")
        
        if account_key in self.accounts:
            account = self.accounts[account_key]
            logging.info(f"Found account: {account['handle']}")
            result = self.login(account['handle'], account['password'])
            if result:
                self.current_account = account_key
                logging.info(f"Switched to account: {account['handle']}")
                return result
            else:
                logging.error(f"Login failed for account: {account['handle']}")
        else:
            logging.error(f"Account key not found: {account_key}")
        return None

    def login(self, handle=None, password=None):
        try:
            logging.info(f"Attempting login with handle: {handle}")
            # If no credentials provided, try to load from environment
            if handle is None:
                handle = os.getenv('BLUESKY_HANDLE')
            if password is None:
                password = os.getenv('BLUESKY_PASSWORD')
            
            # If we still don't have credentials, that's okay - let the caller handle it
            if not handle or not password:
                logging.error("Handle and password are required for login")
                raise ValueError("Handle and password are required for login")
            
            self.profile = self.client.login(handle, password)
            logging.info(f"Successfully logged in as {self.profile.handle}")
            return self.profile
        except Exception as e:
            logging.error(f"Login failed: {e}", exc_info=True)
            return None

    def get_timeline(self, algorithm=None, limit=None):
        try:
            logging.info(f"Fetching timeline with limit={limit}")
            if algorithm:
                timeline = self.client.get_feed(feed=algorithm, limit=limit)
            else:
                timeline = self.client.get_timeline(limit=limit)
            
            logging.info(f"Timeline fetched successfully, type: {type(timeline)}")
            if hasattr(timeline, 'feed'):
                logging.info(f"Timeline feed length: {len(timeline.feed)}")
                return timeline.feed
            else:
                logging.error(f"Timeline object has no 'feed' attribute. Attributes: {dir(timeline)}")
                return []
        except Exception as e:
            logging.error(f"Failed to get timeline: {e}", exc_info=True)
            return []

    def get_author_feed(self, actor, limit=30):
        try:
            logging.info(f"Fetching author feed for {actor}")
            feed = self.client.get_author_feed(actor=actor, limit=limit)
            logging.info(f"Successfully fetched author feed for {actor}. Count: {len(feed.feed) if feed and feed.feed else 0}")
            return feed.feed
        except Exception as e:
            logging.error(f"Failed to get author feed for {actor}: {e}")
            return []

    def get_profile(self, handle=None):
        try:
            if handle is None and self.profile is not None:
                handle = self.profile.handle
            logging.info(f"Fetching profile for {handle}")
            profile = self.client.get_profile(actor=handle)
            logging.info(f"Successfully fetched profile for {handle}")
            return profile
        except Exception as e:
            logging.error(f"Failed to get profile for {handle}: {e}")
            return None

    def get_profiles(self, handles):
        try:
            profiles = self.client.get_profiles(actors=handles)
            return profiles
        except Exception as e:
            logging.error(f"Failed to get profiles: {e}")
            return None

    def send_post(self, text, reply_to=None, embed=None):
        try:
            post = self.client.send_post(text=text, reply_to=reply_to, embed=embed)
            logging.info(f"Successfully posted: {text[:50]}...")
            return post
        except Exception as e:
            logging.error(f"Failed to send post: {e}")
            return None

    def delete_post(self, post_uri):
        try:
            result = self.client.delete_post(post_uri)
            logging.info(f"Successfully deleted post: {post_uri}")
            return result
        except Exception as e:
            logging.error(f"Failed to delete post: {e}")
            return None

    def like(self, uri, cid):
        try:
            like = self.client.like(uri, cid)
            logging.info(f"Successfully liked post: {uri}")
            return like
        except Exception as e:
            logging.error(f"Failed to like post: {e}")
            return None

    def delete_like(self, like_uri):
        try:
            result = self.client.delete_like(like_uri)
            logging.info(f"Successfully unliked post: {like_uri}")
            return result
        except Exception as e:
            logging.error(f"Failed to delete like: {e}")
            return None

    def repost(self, uri, cid):
        try:
            repost = self.client.repost(uri, cid)
            logging.info(f"Successfully reposted: {uri}")
            return repost
        except Exception as e:
            logging.error(f"Failed to repost: {e}")
            return None

    def delete_repost(self, repost_uri):
        try:
            result = self.client.delete_repost(repost_uri)
            logging.info(f"Successfully un-reposted: {repost_uri}")
            return result
        except Exception as e:
            logging.error(f"Failed to delete repost: {e}")
            return None

    def follow(self, did):
        try:
            follow = self.client.follow(did)
            logging.info(f"Successfully followed: {did}")
            return follow
        except Exception as e:
            logging.error(f"Failed to follow: {e}")
            return None

    def unfollow(self, follow_uri):
        try:
            result = self.client.unfollow(follow_uri)
            logging.info(f"Successfully unfollowed: {follow_uri}")
            return result
        except Exception as e:
            logging.error(f"Failed to unfollow: {e}")
            return None

    def get_follows(self, actor, limit=50):
        try:
            follows = self.client.get_follows(actor=actor, limit=limit)
            return follows
        except Exception as e:
            logging.error(f"Failed to get follows: {e}")
            return None

    def get_followers(self, actor, limit=50):
        try:
            followers = self.client.get_followers(actor=actor, limit=limit)
            return followers
        except Exception as e:
            logging.error(f"Failed to get followers: {e}")
            return None

    def search_posts(self, query, limit=25):
        try:
            results = self.client.search_posts(q=query, limit=limit)
            return results.posts
        except Exception as e:
            logging.error(f"Failed to search posts: {e}")
            return []

    def get_notifications(self, limit=50):
        try:
            notifications = self.client.app.bsky.notification.list_notifications(params={'limit': limit})
            return notifications.notifications
        except Exception as e:
            logging.error(f"Failed to get notifications: {e}")
            return []

    def get_posts(self, uris):
        try:
            posts = self.client.app.bsky.feed.get_posts({'uris': uris})
            return posts.posts
        except Exception as e:
            logging.error(f"Failed to get posts: {e}")
            return []

    def get_post_thread(self, uri, depth):
        try:
            thread = self.client.app.bsky.feed.get_post_thread(params={'uri': uri, 'depth': depth})
            return thread
        except Exception as e:
            logging.error(f"Failed to get post thread for uri {uri}: {e}")
            return None

    def get_author_posts(self, actor, limit=100):
        """Get author's posts (excluding replies) with increased limit."""
        try:
            # Get author feed and filter out replies
            feed = self.client.get_author_feed(actor=actor, limit=limit)
            posts_only = [
                item for item in feed.feed 
                if not hasattr(item.post.record, 'reply') or item.post.record.reply is None
            ]
            return posts_only
        except Exception as e:
            logging.error(f"Failed to get author posts: {e}")
            return []

    def get_author_replies(self, actor, limit=100):
        """Get author's replies with increased limit."""
        try:
            # Get author feed and filter for replies only
            feed = self.client.get_author_feed(actor=actor, limit=limit)
            replies_only = [
                item for item in feed.feed 
                if hasattr(item.post.record, 'reply') and item.post.record.reply is not None
            ]
            return replies_only
        except Exception as e:
            logging.error(f"Failed to get author replies: {e}")
            return []

    def get_actor_likes(self, actor, limit=100):
        """Get actor's likes with increased limit."""
        try:
            logging.info(f"Fetching likes for {actor}")
            likes = self.client.app.bsky.feed.get_actor_likes(params={'actor': actor, 'limit': limit})
            logging.info(f"Successfully fetched likes for {actor}. Count: {len(likes.feed) if likes and likes.feed else 0}")
            # Return the feed attribute which contains the liked posts
            return getattr(likes, 'feed', [])
        except Exception as e:
            logging.error(f"Failed to get actor likes: {e}")
            return []

    def get_suggested_follows(self, limit=50):
        try:
            suggestions = self.client.get_suggestions(limit=limit)
            return suggestions
        except Exception as e:
            logging.error(f"Failed to get suggested follows: {e}")
            return None

    def get_actor_feeds(self, actor, limit=50):
        try:
            from atproto_client.models.app.bsky.feed.get_actor_feeds import Params
            feeds = self.client.app.bsky.feed.get_actor_feeds(params=Params(actor=actor, limit=limit))
            return feeds
        except Exception as e:
            logging.error(f"Failed to get actor feeds: {e}")
            return None

    def get_actor_starter_packs(self, actor):
        """Get starter packs that include the actor."""
        try:
            logging.info(f"Fetching starter packs for {actor}")
            starter_packs = self.client.app.bsky.graph.get_actor_starter_packs(params={'actor': actor})
            return starter_packs
        except Exception as e:
            logging.error(f"Failed to get actor starter packs: {e}")
            return None

    def get_likes(self, uri, limit=25):
        """Get likes for a specific post."""
        try:
            likes = self.client.app.bsky.feed.get_likes(params={'uri': uri, 'limit': limit})
            return likes
        except Exception as e:
            logging.error(f"Failed to get likes for {uri}: {e}")
            return None