#!/usr/bin/env python3
"""
User Profiler - Deep Analytics for BlueCLIent
"""

import asyncio
import re
from collections import Counter
from datetime import datetime
import numpy as np
from difflib import SequenceMatcher
import logging
import unicodedata

from textual.app import App, ComposeResult
from textual.binding import Binding
from textual.containers import Vertical, Horizontal, VerticalScroll
from textual.widgets import <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>er, Input, Static, DataTable
from textual.reactive import reactive
from textual import on

from bsky.client import BlueskyClient

class HandleInput(Input):
    """A simple input for entering a user handle."""
    pass

class ProfilerHeader(Horizontal):
    """The header for the profiler, containing the input and button."""
    def compose(self) -> ComposeResult:
        yield HandleInput(placeholder="@username.bsky.social", id="handle-input")
        yield But<PERSON>("Analyze", variant="primary", id="analyze-button")

class ProfileSummary(Static):
    """A widget to display basic profile information."""
    pass

class ActivityHeatmap(DataTable):
    """A widget to display the user's activity heatmap."""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.show_header = True
        self.show_cursor = False
        self.fixed_columns = 1
        self.add_column("Day")
        for hour in range(24):
            self.add_column(f"{hour:02d}")

class InteractionAffinity(DataTable):
    """A widget to display who the user interacts with most."""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.show_header = True
        self.show_cursor = False
        self.add_column("Handle")
        self.add_column("Interaction Score")
        self.add_column("Breakdown (Likes, Replies, Quotes)")

class InteractionSources(DataTable):
    """A widget to display who interacts with the user most."""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.show_header = True
        self.show_cursor = False
        self.add_column("Handle")
        self.add_column("Interaction Score")
        self.add_column("Breakdown (Likes, Replies, Quotes)")

class SubscribedFeeds(DataTable):
    """A widget to display a user's subscribed feeds."""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.show_header = True
        self.show_cursor = False
        self.add_column("Feed Name")
        self.add_column("Creator")
        self.add_column("Likes")

class BotLikelihood(Static):
    """A widget to display the bot likelihood score."""
    pass

class EstimatedTimezone(Static):
    """A widget to display the estimated timezone of a user."""
    pass

class TagAnalysis(DataTable):
    """A widget to display tag usage analysis."""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.show_header = True
        self.show_cursor = False
        self.add_column("Tag")
        self.add_column("Used by User")
        self.add_column("Interacted With")

class EmojiSentiment(DataTable):
    """A widget to display emoji usage analysis."""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.show_header = True
        self.show_cursor = False
        self.add_column("Emoji")
        self.add_column("Used by User")
        self.add_column("In Liked Posts")
        self.add_column("Total")

class ProfilerScreen(Vertical):
    """The main screen for the User Profiler."""
    def compose(self) -> ComposeResult:
        yield ProfilerHeader()
        with VerticalScroll(id="results-container"):
            yield ProfileSummary(id="profile-summary")
            yield BotLikelihood(id="bot-likelihood")
            yield EstimatedTimezone(id="estimated-timezone")
            yield ActivityHeatmap(id="activity-heatmap")
            yield TagAnalysis(id="tag-analysis")
            yield EmojiSentiment(id="emoji-sentiment")
            yield InteractionAffinity(id="interaction-affinity")
            yield InteractionSources(id="interaction-sources")
            yield SubscribedFeeds(id="subscribed-feeds")

class ProfilerApp(App[None]):
    """The main application for the User Profiler."""
    CSS_PATH = "shell.scss"
    BINDINGS = [
        Binding("ctrl+q", "quit", "Quit"),
        Binding("ctrl+d", "show_debug", "Debug")
    ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        logging.info("Initializing ProfilerApp...")
        self.client = BlueskyClient()
        accounts = self.client.get_account_list()
        logging.info(f"Found {len(accounts)} accounts: {[acc[1] for acc in accounts]}")
        if accounts:
            result = self.client.switch_account(accounts[0][0])
            if result:
                logging.info(f"Successfully logged in as: {result.handle}")
            else:
                logging.error("Failed to login with first account")
        else:
            logging.warning("No accounts found in environment")

    def compose(self) -> ComposeResult:
        yield Header(show_clock=True)
        yield ProfilerScreen()
        yield Footer()

    def sanitize_handle_value(self, value: str) -> str:
        """Remove invisible characters from handle input."""
        # Remove various invisible/formatting characters:
        # \u200B-\u200F: Zero-width spaces and directional marks
        # \u202A-\u202E: Directional formatting characters
        # \u2060-\u2064: Word joiner and invisible operators
        # \uFEFF: Byte order mark
        # \u00AD: Soft hyphen
        sanitized = re.sub(r'[\u200B-\u200F\u202A-\u202E\u2060-\u2064\uFEFF\u00AD]', '', value)
        logging.info(f"Sanitized handle: '{value}' -> '{sanitized}' (removed {len(value) - len(sanitized)} characters)")
        return sanitized

    async def on_button_pressed(self, event: Button.Pressed) -> None:
        if event.button.id == "analyze-button":
            handle_input = self.query_one(HandleInput)
            # Sanitize the input value before processing
            sanitized_value = self.sanitize_handle_value(handle_input.value)
            handle_input.value = sanitized_value
            await self.run_analysis()

    async def run_analysis(self) -> None:
        handle_input = self.query_one(HandleInput)
        handle = handle_input.value.strip()
        if not handle:
            self.notify("Please enter a user handle.", severity="error")
            return
        if handle.startswith('@'):
            handle = handle[1:]

        logging.info(f"Starting analysis for handle: {handle}")
        self.notify(f"Analyzing {handle}...")
        for widget_id in ["#profile-summary", "#bot-likelihood", "#estimated-timezone"]:
            self.query_one(widget_id, Static).update("Loading...")
        for widget_id in ["#activity-heatmap", "#interaction-affinity", "#interaction-sources", "#subscribed-feeds", "#tag-analysis", "#emoji-sentiment"]:
            self.query_one(widget_id, DataTable).clear()

        # Check if client is logged in
        if not self.client.profile:
            logging.error("Client is not logged in")
            self.notify("Error: Not logged in to Bluesky. Please check your credentials.", severity="error")
            return

        logging.info(f"Client logged in as: {self.client.profile.handle}")

        try:
            logging.info("Starting parallel data fetch...")
            profile_task = asyncio.to_thread(self.client.get_profile, handle)
            feed_task = asyncio.to_thread(self.client.get_author_feed, handle, limit=100)
            likes_task = asyncio.to_thread(self.client.get_actor_likes, handle, limit=100)

            profile, feed, likes = await asyncio.gather(profile_task, feed_task, likes_task, return_exceptions=True)

            # Log results of each task with detailed error information
            if isinstance(profile, Exception):
                logging.error(f"Profile fetch failed: {type(profile).__name__}: {profile}")
            else:
                logging.info(f"Profile fetch successful: {profile.handle if profile and hasattr(profile, 'handle') else 'No handle'}")

            if isinstance(feed, Exception):
                logging.error(f"Feed fetch failed: {type(feed).__name__}: {feed}")
            else:
                logging.info(f"Feed fetch successful: {len(feed) if feed else 0} items")

            if isinstance(likes, Exception):
                logging.error(f"Likes fetch failed: {type(likes).__name__}: {likes}")
            else:
                logging.info(f"Likes fetch successful: {len(likes) if likes else 0} items")

        except Exception as e:
            logging.error(f"Failed to fetch initial data: {e}", exc_info=True)
            self.notify(f"Failed to fetch initial data: {e}", severity="error")
            return

        await asyncio.gather(
            self.update_profile_summary(handle, profile),
            self.update_activity_heatmap(feed, likes),
            self.update_interaction_affinity(handle, feed, likes),
            self.update_interaction_sources(handle, profile),
            self.update_subscribed_feeds(handle),
            self.update_bot_likelihood(profile, feed),
            self.update_tag_analysis(feed, likes),
            self.update_emoji_sentiment(feed, likes),
            self.update_timezone_analysis(feed, likes)
        )

    async def update_profile_summary(self, handle: str, profile) -> None:
        widget = self.query_one("#profile-summary", ProfileSummary)
        if not profile or isinstance(profile, Exception):
            error_msg = f"Error: {profile}" if isinstance(profile, Exception) else "Profile is None/empty"
            logging.error(f"Profile update failed for {handle}: {error_msg}")
            widget.update(f"[b]Profile Summary for @{handle}[/b]\n\n[red]Could not load profile.[/red]\n[dim]{error_msg}[/dim]")
            return
        summary = (
            f"[b]Profile Summary for @{profile.handle}[/b]\n\n"
            f"[b]Display Name:[/b] {getattr(profile, 'display_name', 'N/A')}\n"
            f"[b]Followers:[/b] {getattr(profile, 'followers_count', 'N/A')}\n"
            f"[b]Following:[/b] {getattr(profile, 'follows_count', 'N/A')}\n"
            f"[b]Posts:[/b] {getattr(profile, 'posts_count', 'N/A')}\n"
            f"[b]Description:[/b] {getattr(profile, 'description', 'N/A').replace('[', '(').replace(']', ')')}"
        )
        widget.update(summary)

    async def update_activity_heatmap(self, feed, likes) -> None:
        widget = self.query_one("#activity-heatmap", ActivityHeatmap)
        try:
            post_timestamps = [p.post.record.created_at for p in feed if hasattr(p, 'post') and hasattr(p.post, 'record') and hasattr(p.post.record, 'created_at')]
            like_timestamps = [like.record.created_at for like in likes if hasattr(like, 'record') and hasattr(like.record, 'created_at')]
            timestamps = post_timestamps + like_timestamps

            if not timestamps:
                widget.add_row("No posts or likes found in the last 100 items.")
                return

            activity = [[0] * 24 for _ in range(7)]
            days = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
            for ts in timestamps:
                try:
                    dt = datetime.fromisoformat(ts.replace("Z", "+00:00"))
                    activity[dt.weekday()][dt.hour] += 1
                except (ValueError, IndexError):
                    continue

            max_activity = max(max(day) for day in activity) or 1
            for day_index, day_name in enumerate(days):
                row_data = [day_name]
                for hour_activity in activity[day_index]:
                    if hour_activity == 0:
                        row_data.append(" ")
                    else:
                        intensity = int((hour_activity / max_activity) * 200) + 55
                        color = f"rgb(0,{min(intensity, 255)},0)"
                        row_data.append(f"[on {color}] {hour_activity} [/]")
                widget.add_row(*row_data)
        except Exception as e:
            widget.add_row(f"[red]Error rendering heatmap: {e}[/red]")

    async def update_interaction_affinity(self, handle: str, feed, likes) -> None:
        widget = self.query_one("#interaction-affinity", InteractionAffinity)
        try:
            author_scores = Counter()
            author_breakdown = {}
            if likes:
                for like in likes:
                    try:
                        author = like.post.author.handle
                        author_scores[author] += 1
                        author_breakdown.setdefault(author, Counter())["likes"] += 1
                    except AttributeError:
                        continue
            if feed:
                for item in feed:
                    try:
                        post = item.post
                        if post.record and post.record.reply and hasattr(post.record.reply, 'parent') and hasattr(post.record.reply.parent, 'uri'):
                            parent_uri = post.record.reply.parent.uri
                            parent_post_thread = await asyncio.to_thread(self.client.get_post_thread, parent_uri, depth=0)
                            if parent_post_thread and hasattr(parent_post_thread, 'thread') and hasattr(parent_post_thread.thread, 'post'):
                                author = parent_post_thread.thread.post.author.handle
                                author_scores[author] += 3
                                author_breakdown.setdefault(author, Counter())["replies"] += 1
                        if post.embed and hasattr(post.embed, 'record') and hasattr(post.embed.record, 'author'):
                            author = post.embed.record.author.handle
                            author_scores[author] += 2
                            author_breakdown.setdefault(author, Counter())["quotes"] += 1
                    except AttributeError:
                        continue
            
            if handle in author_scores:
                del author_scores[handle]

            if not author_scores:
                widget.add_row("No external interactions found.")
                return

            for author, score in author_scores.most_common(10):
                b = author_breakdown.get(author, Counter())
                breakdown_str = f"{b.get('likes', 0)}L, {b.get('replies', 0)}R, {b.get('quotes', 0)}Q"
                widget.add_row(f"@{author}", str(score), breakdown_str)
        except Exception as e:
            widget.add_row(f"[red]Error processing interactions: {e}[/red]")

    async def update_interaction_sources(self, handle: str, profile) -> None:
        """Show who interacts with the user (who likes/replies to their posts)."""
        widget = self.query_one("#interaction-sources", InteractionSources)
        try:
            if not profile or isinstance(profile, Exception):
                widget.add_row("Could not analyze interaction sources - profile not available.")
                return

            # Get the user's recent posts to see who interacts with them
            user_feed = await asyncio.to_thread(self.client.get_author_feed, handle, limit=50)
            if not user_feed or isinstance(user_feed, Exception):
                widget.add_row("Could not fetch user's posts for interaction analysis.")
                return

            author_scores = Counter()
            author_breakdown = {}

            # Analyze who likes and replies to the user's posts
            for post_item in user_feed:
                try:
                    post_uri = post_item.post.uri

                    # Get likes for this post
                    try:
                        likes_response = await asyncio.to_thread(self.client.get_likes, post_uri, limit=25)
                        if likes_response and hasattr(likes_response, 'likes'):
                            for like in likes_response.likes:
                                if hasattr(like, 'actor') and hasattr(like.actor, 'handle'):
                                    author = like.actor.handle
                                    if author != handle:  # Exclude self
                                        author_scores[author] += 1
                                        author_breakdown.setdefault(author, Counter())["likes"] += 1
                    except Exception as e:
                        logging.debug(f"Could not get likes for post {post_uri}: {e}")
                        continue

                    # Get replies to this post
                    try:
                        thread_response = await asyncio.to_thread(self.client.get_post_thread, post_uri, depth=1)
                        if thread_response and hasattr(thread_response, 'thread') and hasattr(thread_response.thread, 'replies'):
                            for reply in thread_response.thread.replies:
                                if hasattr(reply, 'post') and hasattr(reply.post, 'author') and hasattr(reply.post.author, 'handle'):
                                    author = reply.post.author.handle
                                    if author != handle:  # Exclude self
                                        author_scores[author] += 3  # Replies are worth more than likes
                                        author_breakdown.setdefault(author, Counter())["replies"] += 1
                    except Exception as e:
                        logging.debug(f"Could not get replies for post {post_uri}: {e}")
                        continue

                except AttributeError:
                    continue

            if not author_scores:
                widget.add_row("No external interactions found on user's posts.")
                return

            for author, score in author_scores.most_common(10):
                b = author_breakdown.get(author, Counter())
                breakdown_str = f"{b.get('likes', 0)}L, {b.get('replies', 0)}R, {b.get('quotes', 0)}Q"
                widget.add_row(f"@{author}", str(score), breakdown_str)

        except Exception as e:
            widget.add_row(f"[red]Error analyzing interaction sources: {e}[/red]")

    async def update_subscribed_feeds(self, handle: str) -> None:
        widget = self.query_one("#subscribed-feeds", SubscribedFeeds)
        try:
            response = await asyncio.to_thread(self.client.get_actor_feeds, handle)
            if not response or isinstance(response, Exception) or not hasattr(response, 'feeds') or not response.feeds:
                widget.add_row("No subscribed feeds found.")
                return
            for feed in response.feeds:
                widget.add_row(feed.display_name, f"@{feed.creator.handle}", str(feed.like_count))
        except Exception as e:
            widget.add_row(f"[red]Error: {e}[/red]")

    async def update_bot_likelihood(self, profile, feed) -> None:
        widget = self.query_one("#bot-likelihood", BotLikelihood)
        try:
            if not profile or isinstance(profile, Exception):
                error_msg = f"Error: {profile}" if isinstance(profile, Exception) else "Profile is None/empty"
                logging.error(f"Bot analysis failed: {error_msg}")
                widget.update(f"[b]Bot Likelihood Score[/b]\n\n[red]Could not load profile for bot analysis.[/red]\n[dim]{error_msg}[/dim]")
                return

            scores = {}
            followers = profile.followers_count or 0
            following = profile.follows_count or 0
            
            if following > 50 and followers < (following * 0.2):
                scores["Follow Ratio"] = 25
            elif following > 50 and followers < (following * 0.5):
                scores["Follow Ratio"] = 15
            else:
                scores["Follow Ratio"] = 0

            if len(feed) > 3:
                post_times = [datetime.fromisoformat(p.post.record.created_at.replace("Z", "+00:00")) for p in feed]
                deltas = [(post_times[i] - post_times[i+1]).total_seconds() for i in range(len(post_times)-1)]
                if len(deltas) > 1:
                    std_dev = np.std(deltas)
                    if std_dev < 60: scores["Post Interval"] = 40
                    elif std_dev < 600: scores["Post Interval"] = 20
                    else: scores["Post Interval"] = 0
                else:
                    scores["Post Interval"] = 0
                texts = [p.post.record.text for p in feed]
                if len(texts) > 1:
                    total_similarity, pairs = 0, 0
                    for i in range(len(texts)):
                        for j in range(i + 1, len(texts)):
                            total_similarity += SequenceMatcher(None, texts[i], texts[j]).ratio()
                            pairs += 1
                    avg_similarity = (total_similarity / pairs) * 100
                    if avg_similarity > 80: scores["Content Similarity"] = 35
                    elif avg_similarity > 50: scores["Content Similarity"] = 15
                    else: scores["Content Similarity"] = 0
                else:
                    scores["Content Similarity"] = 0
            else:
                scores["Post Interval"] = 0
                scores["Content Similarity"] = 0

            total_score = sum(scores.values())
            result_str = f"[b]Bot Likelihood Score: {total_score}%[/b]\n"
            result_str += "[dim]This score is a heuristic guess based on public data.\n - High follow/follower ratio, very regular posting times,\n   and highly similar posts can indicate bot activity.[/dim]\n\n"
            result_str += f"- Follower/Following Ratio: {scores.get('Follow Ratio', 0)}/25\n"
            result_str += f"- Post Interval Consistency: {scores.get('Post Interval', 0)}/40\n"
            result_str += f"- Content Similarity: {scores.get('Content Similarity', 0)}/35\n"
            widget.update(result_str)
        except Exception as e:
            widget.update(f"[b]Bot Likelihood Score[/b]\n\n[red]Error: {e}[/red]")

    async def update_tag_analysis(self, feed, likes) -> None:
        widget = self.query_one("#tag-analysis", TagAnalysis)
        try:
            used_tags = Counter()
            interacted_tags = Counter()

            if feed:
                for item in feed:
                    if hasattr(item, 'post') and hasattr(item.post, 'record') and hasattr(item.post.record, 'text'):
                        tags = re.findall(r'#(\w+)', item.post.record.text)
                        used_tags.update(tags)
            
            if likes:
                for like in likes:
                    if hasattr(like, 'post') and hasattr(like.post, 'record') and hasattr(like.post.record, 'text'):
                        tags = re.findall(r'#(\w+)', like.post.record.text)
                        interacted_tags.update(tags)

            all_tags = set(used_tags.keys()) | set(interacted_tags.keys())

            if not all_tags:
                widget.add_row("No tag usage found.")
                return

            sorted_tags = sorted(all_tags, key=lambda t: (used_tags.get(t, 0) + interacted_tags.get(t, 0)), reverse=True)

            for tag in sorted_tags[:10]:
                widget.add_row(f"#{tag}", str(used_tags.get(tag, 0)), str(interacted_tags.get(tag, 0)))

        except Exception as e:
            widget.add_row(f"[red]Error: {e}[/red]")

    def extract_emojis(self, text: str) -> list:
        """Extract emojis from text using regex pattern."""
        if not text:
            return []

        # Regex pattern to match emojis
        emoji_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"  # emoticons
            "\U0001F300-\U0001F5FF"  # symbols & pictographs
            "\U0001F680-\U0001F6FF"  # transport & map symbols
            "\U0001F1E0-\U0001F1FF"  # flags (iOS)
            "\U00002702-\U000027B0"  # dingbats
            "\U000024C2-\U0001F251"  # enclosed characters
            "\U0001F900-\U0001F9FF"  # supplemental symbols and pictographs
            "\U0001FA70-\U0001FAFF"  # symbols and pictographs extended-a
            "]+",
            flags=re.UNICODE
        )

        return emoji_pattern.findall(text)

    async def update_emoji_sentiment(self, feed, likes) -> None:
        """Analyze emoji usage in user's posts and liked content."""
        widget = self.query_one("#emoji-sentiment", EmojiSentiment)
        try:
            user_emojis = Counter()
            liked_emojis = Counter()

            # Extract emojis from user's own posts
            if feed:
                for item in feed:
                    if hasattr(item, 'post') and hasattr(item.post, 'record') and hasattr(item.post.record, 'text'):
                        text = item.post.record.text
                        emojis = self.extract_emojis(text)
                        user_emojis.update(emojis)

            # Extract emojis from posts the user liked
            if likes:
                for like in likes:
                    if hasattr(like, 'post') and hasattr(like.post, 'record') and hasattr(like.post.record, 'text'):
                        text = like.post.record.text
                        emojis = self.extract_emojis(text)
                        liked_emojis.update(emojis)

            # Combine all emojis and get top 10
            all_emojis = set(user_emojis.keys()) | set(liked_emojis.keys())

            if not all_emojis:
                widget.add_row("No emojis found in posts or likes.")
                return

            # Sort by total usage (user + liked)
            sorted_emojis = sorted(all_emojis,
                                 key=lambda e: (user_emojis.get(e, 0) + liked_emojis.get(e, 0)),
                                 reverse=True)

            for emoji in sorted_emojis[:10]:
                user_count = user_emojis.get(emoji, 0)
                liked_count = liked_emojis.get(emoji, 0)
                total_count = user_count + liked_count
                widget.add_row(emoji, str(user_count), str(liked_count), str(total_count))

        except Exception as e:
            widget.add_row(f"[red]Error analyzing emojis: {e}[/red]")

    async def update_timezone_analysis(self, feed, likes) -> None:
        widget = self.query_one("#estimated-timezone", EstimatedTimezone)
        try:
            post_timestamps = [p.post.record.created_at for p in feed if hasattr(p, 'post') and hasattr(p.post, 'record') and hasattr(p.post.record, 'created_at')]
            like_timestamps = [like.record.created_at for like in likes if hasattr(like, 'record') and hasattr(like.record, 'created_at')]
            timestamps = post_timestamps + like_timestamps

            if not timestamps:
                widget.update("[b]Estimated Timezone[/b]\n\nNot enough data for analysis.")
                return

            hour_counts = Counter(datetime.fromisoformat(ts.replace("Z", "+00:00")).hour for ts in timestamps)
            if not hour_counts:
                widget.update("[b]Estimated Timezone[/b]\n\nNot enough data for analysis.")
                return

            peak_hour_utc = hour_counts.most_common(1)[0][0]
            
            # Assumes peak usage is between 7 PM (19:00) and 10 PM (22:00) local time
            utc_offset_min = 19 - peak_hour_utc
            utc_offset_max = 22 - peak_hour_utc

            def format_offset(offset):
                # Wrap around 24h clock
                offset = (offset + 12) % 24 - 12
                return f"UTC{offset:+}"

            widget.update(f"[b]Estimated Timezone[/b]\n\nPeak activity around {peak_hour_utc:02d}:00 UTC suggests a timezone between {format_offset(utc_offset_min)} and {format_offset(utc_offset_max)}.")

        except Exception as e:
            widget.update(f"[b]Estimated Timezone[/b]\n\n[red]Error: {e}[/red]")

    def action_show_debug(self) -> None:
        """Show debug information."""
        try:
            with open('bluesky_tui.log', 'r') as f:
                log_content = f.read()
                # Show last 50 lines
                lines = log_content.split('\n')
                recent_lines = lines[-50:] if len(lines) > 50 else lines
                debug_info = '\n'.join(recent_lines)

            client_info = f"Client logged in: {bool(self.client.profile)}"
            if self.client.profile:
                client_info += f" as {self.client.profile.handle}"

            accounts_info = f"Available accounts: {len(self.client.get_account_list())}"

            full_debug = f"{client_info}\n{accounts_info}\n\nRecent logs:\n{debug_info}"
            self.notify(f"Debug Info:\n{full_debug}", timeout=10)
        except Exception as e:
            self.notify(f"Debug error: {e}", severity="error")

if __name__ == "__main__":
    app = ProfilerApp()
    app.run()