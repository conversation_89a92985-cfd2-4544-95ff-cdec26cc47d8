#!/usr/bin/env python3
"""
User Profiler - Deep Analytics for BlueCLIent
"""

import asyncio
import re
from collections import Counter
from datetime import datetime
import numpy as np
from difflib import SequenceMatcher
import logging

from textual.app import App, ComposeResult
from textual.binding import Binding
from textual.containers import Vertical, Horizontal, VerticalScroll
from textual.widgets import <PERSON><PERSON>, <PERSON><PERSON>, Header, Input, Static, DataTable
from textual.reactive import reactive
from textual import on

from bsky.client import BlueskyClient

class HandleInput(Input):
    """A simple input for entering a user handle."""
    pass

class ProfilerHeader(Horizontal):
    """The header for the profiler, containing the input and button."""
    def compose(self) -> ComposeResult:
        yield HandleInput(placeholder="@username.bsky.social", id="handle-input")
        yield But<PERSON>("Analyze", variant="primary", id="analyze-button")

class ProfileSummary(Static):
    """A widget to display basic profile information."""
    pass

class ActivityHeatmap(DataTable):
    """A widget to display the user's activity heatmap."""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.show_header = True
        self.show_cursor = False
        self.fixed_columns = 1
        self.add_column("Day")
        for hour in range(24):
            self.add_column(f"{hour:02d}")

class InteractionAffinity(DataTable):
    """A widget to display user interaction affinity."""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.show_header = True
        self.show_cursor = False
        self.add_column("Handle")
        self.add_column("Interaction Score")
        self.add_column("Breakdown (Likes, Replies, Quotes)")

class SubscribedFeeds(DataTable):
    """A widget to display a user's subscribed feeds."""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.show_header = True
        self.show_cursor = False
        self.add_column("Feed Name")
        self.add_column("Creator")
        self.add_column("Likes")

class BotLikelihood(Static):
    """A widget to display the bot likelihood score."""
    pass

class EstimatedTimezone(Static):
    """A widget to display the estimated timezone of a user."""
    pass

class TagAnalysis(DataTable):
    """A widget to display tag usage analysis."""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.show_header = True
        self.show_cursor = False
        self.add_column("Tag")
        self.add_column("Used by User")
        self.add_column("Interacted With")

class ProfilerScreen(Vertical):
    """The main screen for the User Profiler."""
    def compose(self) -> ComposeResult:
        yield ProfilerHeader()
        with VerticalScroll(id="results-container"):
            yield ProfileSummary(id="profile-summary")
            yield BotLikelihood(id="bot-likelihood")
            yield EstimatedTimezone(id="estimated-timezone")
            yield ActivityHeatmap(id="activity-heatmap")
            yield TagAnalysis(id="tag-analysis")
            yield InteractionAffinity(id="interaction-affinity")
            yield SubscribedFeeds(id="subscribed-feeds")

class ProfilerApp(App[None]):
    """The main application for the User Profiler."""
    CSS_PATH = "shell.scss"
    BINDINGS = [Binding("ctrl+q", "quit", "Quit")]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        logging.info("Initializing ProfilerApp...")
        self.client = BlueskyClient()
        accounts = self.client.get_account_list()
        logging.info(f"Found {len(accounts)} accounts: {[acc[1] for acc in accounts]}")
        if accounts:
            result = self.client.switch_account(accounts[0][0])
            if result:
                logging.info(f"Successfully logged in as: {result.handle}")
            else:
                logging.error("Failed to login with first account")
        else:
            logging.warning("No accounts found in environment")

    def compose(self) -> ComposeResult:
        yield Header(show_clock=True)
        yield ProfilerScreen()
        yield Footer()

    def sanitize_handle_value(self, value: str) -> str:
        """Remove invisible characters from handle input."""
        return re.sub(r'[\u200B-\u200D\uFEFF]', '', value)

    async def on_button_pressed(self, event: Button.Pressed) -> None:
        if event.button.id == "analyze-button":
            handle_input = self.query_one(HandleInput)
            # Sanitize the input value before processing
            sanitized_value = self.sanitize_handle_value(handle_input.value)
            handle_input.value = sanitized_value
            await self.run_analysis()

    async def run_analysis(self) -> None:
        handle_input = self.query_one(HandleInput)
        handle = handle_input.value.strip()
        if not handle:
            self.notify("Please enter a user handle.", severity="error")
            return
        if handle.startswith('@'):
            handle = handle[1:]

        logging.info(f"Starting analysis for handle: {handle}")
        self.notify(f"Analyzing {handle}...")
        for widget_id in ["#profile-summary", "#bot-likelihood", "#estimated-timezone"]:
            self.query_one(widget_id, Static).update("Loading...")
        for widget_id in ["#activity-heatmap", "#interaction-affinity", "#subscribed-feeds", "#tag-analysis"]:
            self.query_one(widget_id, DataTable).clear()

        # Check if client is logged in
        if not self.client.profile:
            logging.error("Client is not logged in")
            self.notify("Error: Not logged in to Bluesky. Please check your credentials.", severity="error")
            return

        logging.info(f"Client logged in as: {self.client.profile.handle}")

        try:
            logging.info("Starting parallel data fetch...")
            profile_task = asyncio.to_thread(self.client.get_profile, handle)
            feed_task = asyncio.to_thread(self.client.get_author_feed, handle, limit=100)
            likes_task = asyncio.to_thread(self.client.get_actor_likes, handle, limit=100)

            profile, feed, likes = await asyncio.gather(profile_task, feed_task, likes_task, return_exceptions=True)

            # Log results of each task
            logging.info(f"Profile result: {type(profile)} - {profile if isinstance(profile, Exception) else 'Success'}")
            logging.info(f"Feed result: {type(feed)} - {feed if isinstance(feed, Exception) else f'Success, {len(feed) if feed else 0} items'}")
            logging.info(f"Likes result: {type(likes)} - {likes if isinstance(likes, Exception) else f'Success, {len(likes) if likes else 0} items'}")

        except Exception as e:
            logging.error(f"Failed to fetch initial data: {e}", exc_info=True)
            self.notify(f"Failed to fetch initial data: {e}", severity="error")
            return

        await asyncio.gather(
            self.update_profile_summary(handle, profile),
            self.update_activity_heatmap(feed, likes),
            self.update_interaction_affinity(handle, feed, likes),
            self.update_subscribed_feeds(handle),
            self.update_bot_likelihood(profile, feed),
            self.update_tag_analysis(feed, likes),
            self.update_timezone_analysis(feed, likes)
        )

    async def update_profile_summary(self, handle: str, profile) -> None:
        widget = self.query_one("#profile-summary", ProfileSummary)
        if not profile or isinstance(profile, Exception):
            error_msg = f"Error: {profile}" if isinstance(profile, Exception) else "Profile is None/empty"
            logging.error(f"Profile update failed for {handle}: {error_msg}")
            widget.update(f"[b]Profile Summary for @{handle}[/b]\n\n[red]Could not load profile.[/red]\n[dim]{error_msg}[/dim]")
            return
        summary = (
            f"[b]Profile Summary for @{profile.handle}[/b]\n\n"
            f"[b]Display Name:[/b] {getattr(profile, 'display_name', 'N/A')}\n"
            f"[b]Followers:[/b] {getattr(profile, 'followers_count', 'N/A')}\n"
            f"[b]Following:[/b] {getattr(profile, 'follows_count', 'N/A')}\n"
            f"[b]Posts:[/b] {getattr(profile, 'posts_count', 'N/A')}\n"
            f"[b]Description:[/b] {getattr(profile, 'description', 'N/A').replace('[', '(').replace(']', ')')}"
        )
        widget.update(summary)

    async def update_activity_heatmap(self, feed, likes) -> None:
        widget = self.query_one("#activity-heatmap", ActivityHeatmap)
        try:
            post_timestamps = [p.post.record.created_at for p in feed if hasattr(p, 'post') and hasattr(p.post, 'record') and hasattr(p.post.record, 'created_at')]
            like_timestamps = [like.record.created_at for like in likes if hasattr(like, 'record') and hasattr(like.record, 'created_at')]
            timestamps = post_timestamps + like_timestamps

            if not timestamps:
                widget.add_row("No posts or likes found in the last 100 items.")
                return

            activity = [[0] * 24 for _ in range(7)]
            days = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
            for ts in timestamps:
                try:
                    dt = datetime.fromisoformat(ts.replace("Z", "+00:00"))
                    activity[dt.weekday()][dt.hour] += 1
                except (ValueError, IndexError):
                    continue

            max_activity = max(max(day) for day in activity) or 1
            for day_index, day_name in enumerate(days):
                row_data = [day_name]
                for hour_activity in activity[day_index]:
                    if hour_activity == 0:
                        row_data.append(" ")
                    else:
                        intensity = int((hour_activity / max_activity) * 200) + 55
                        color = f"rgb(0,{min(intensity, 255)},0)"
                        row_data.append(f"[on {color}] {hour_activity} [/]")
                widget.add_row(*row_data)
        except Exception as e:
            widget.add_row(f"[red]Error rendering heatmap: {e}[/red]")

    async def update_interaction_affinity(self, handle: str, feed, likes) -> None:
        widget = self.query_one("#interaction-affinity", InteractionAffinity)
        try:
            author_scores = Counter()
            author_breakdown = {}
            if likes:
                for like in likes:
                    try:
                        author = like.post.author.handle
                        author_scores[author] += 1
                        author_breakdown.setdefault(author, Counter())["likes"] += 1
                    except AttributeError:
                        continue
            if feed:
                for item in feed:
                    try:
                        post = item.post
                        if post.record and post.record.reply and hasattr(post.record.reply, 'parent') and hasattr(post.record.reply.parent, 'uri'):
                            parent_uri = post.record.reply.parent.uri
                            parent_post_thread = await asyncio.to_thread(self.client.get_post_thread, parent_uri, depth=0)
                            if parent_post_thread and hasattr(parent_post_thread, 'thread') and hasattr(parent_post_thread.thread, 'post'):
                                author = parent_post_thread.thread.post.author.handle
                                author_scores[author] += 3
                                author_breakdown.setdefault(author, Counter())["replies"] += 1
                        if post.embed and hasattr(post.embed, 'record') and hasattr(post.embed.record, 'author'):
                            author = post.embed.record.author.handle
                            author_scores[author] += 2
                            author_breakdown.setdefault(author, Counter())["quotes"] += 1
                    except AttributeError:
                        continue
            
            if handle in author_scores:
                del author_scores[handle]

            if not author_scores:
                widget.add_row("No external interactions found.")
                return

            for author, score in author_scores.most_common(10):
                b = author_breakdown.get(author, Counter())
                breakdown_str = f"{b.get('likes', 0)}L, {b.get('replies', 0)}R, {b.get('quotes', 0)}Q"
                widget.add_row(f"@{author}", str(score), breakdown_str)
        except Exception as e:
            widget.add_row(f"[red]Error processing interactions: {e}[/red]")

    async def update_subscribed_feeds(self, handle: str) -> None:
        widget = self.query_one("#subscribed-feeds", SubscribedFeeds)
        try:
            response = await asyncio.to_thread(self.client.get_actor_feeds, handle)
            if not response or isinstance(response, Exception) or not hasattr(response, 'feeds') or not response.feeds:
                widget.add_row("No subscribed feeds found.")
                return
            for feed in response.feeds:
                widget.add_row(feed.display_name, f"@{feed.creator.handle}", str(feed.like_count))
        except Exception as e:
            widget.add_row(f"[red]Error: {e}[/red]")

    async def update_bot_likelihood(self, profile, feed) -> None:
        widget = self.query_one("#bot-likelihood", BotLikelihood)
        try:
            if not profile or isinstance(profile, Exception):
                error_msg = f"Error: {profile}" if isinstance(profile, Exception) else "Profile is None/empty"
                logging.error(f"Bot analysis failed: {error_msg}")
                widget.update(f"[b]Bot Likelihood Score[/b]\n\n[red]Could not load profile for bot analysis.[/red]\n[dim]{error_msg}[/dim]")
                return

            scores = {}
            followers = profile.followers_count or 0
            following = profile.follows_count or 0
            
            if following > 50 and followers < (following * 0.2):
                scores["Follow Ratio"] = 25
            elif following > 50 and followers < (following * 0.5):
                scores["Follow Ratio"] = 15
            else:
                scores["Follow Ratio"] = 0

            if len(feed) > 3:
                post_times = [datetime.fromisoformat(p.post.record.created_at.replace("Z", "+00:00")) for p in feed]
                deltas = [(post_times[i] - post_times[i+1]).total_seconds() for i in range(len(post_times)-1)]
                if len(deltas) > 1:
                    std_dev = np.std(deltas)
                    if std_dev < 60: scores["Post Interval"] = 40
                    elif std_dev < 600: scores["Post Interval"] = 20
                    else: scores["Post Interval"] = 0
                else:
                    scores["Post Interval"] = 0
                texts = [p.post.record.text for p in feed]
                if len(texts) > 1:
                    total_similarity, pairs = 0, 0
                    for i in range(len(texts)):
                        for j in range(i + 1, len(texts)):
                            total_similarity += SequenceMatcher(None, texts[i], texts[j]).ratio()
                            pairs += 1
                    avg_similarity = (total_similarity / pairs) * 100
                    if avg_similarity > 80: scores["Content Similarity"] = 35
                    elif avg_similarity > 50: scores["Content Similarity"] = 15
                    else: scores["Content Similarity"] = 0
                else:
                    scores["Content Similarity"] = 0
            else:
                scores["Post Interval"] = 0
                scores["Content Similarity"] = 0

            total_score = sum(scores.values())
            result_str = f"[b]Bot Likelihood Score: {total_score}%[/b]\n"
            result_str += "[dim]This score is a heuristic guess based on public data.\n - High follow/follower ratio, very regular posting times,\n   and highly similar posts can indicate bot activity.[/dim]\n\n"
            result_str += f"- Follower/Following Ratio: {scores.get('Follow Ratio', 0)}/25\n"
            result_str += f"- Post Interval Consistency: {scores.get('Post Interval', 0)}/40\n"
            result_str += f"- Content Similarity: {scores.get('Content Similarity', 0)}/35\n"
            widget.update(result_str)
        except Exception as e:
            widget.update(f"[b]Bot Likelihood Score[/b]\n\n[red]Error: {e}[/red]")

    async def update_tag_analysis(self, feed, likes) -> None:
        widget = self.query_one("#tag-analysis", TagAnalysis)
        try:
            used_tags = Counter()
            interacted_tags = Counter()

            if feed:
                for item in feed:
                    if hasattr(item, 'post') and hasattr(item.post, 'record') and hasattr(item.post.record, 'text'):
                        tags = re.findall(r'#(\w+)', item.post.record.text)
                        used_tags.update(tags)
            
            if likes:
                for like in likes:
                    if hasattr(like, 'post') and hasattr(like.post, 'record') and hasattr(like.post.record, 'text'):
                        tags = re.findall(r'#(\w+)', like.post.record.text)
                        interacted_tags.update(tags)

            all_tags = set(used_tags.keys()) | set(interacted_tags.keys())

            if not all_tags:
                widget.add_row("No tag usage found.")
                return

            sorted_tags = sorted(all_tags, key=lambda t: (used_tags.get(t, 0) + interacted_tags.get(t, 0)), reverse=True)

            for tag in sorted_tags[:10]:
                widget.add_row(f"#{tag}", str(used_tags.get(tag, 0)), str(interacted_tags.get(tag, 0)))

        except Exception as e:
            widget.add_row(f"[red]Error: {e}[/red]")

    async def update_timezone_analysis(self, feed, likes) -> None:
        widget = self.query_one("#estimated-timezone", EstimatedTimezone)
        try:
            post_timestamps = [p.post.record.created_at for p in feed if hasattr(p, 'post') and hasattr(p.post, 'record') and hasattr(p.post.record, 'created_at')]
            like_timestamps = [like.record.created_at for like in likes if hasattr(like, 'record') and hasattr(like.record, 'created_at')]
            timestamps = post_timestamps + like_timestamps

            if not timestamps:
                widget.update("[b]Estimated Timezone[/b]\n\nNot enough data for analysis.")
                return

            hour_counts = Counter(datetime.fromisoformat(ts.replace("Z", "+00:00")).hour for ts in timestamps)
            if not hour_counts:
                widget.update("[b]Estimated Timezone[/b]\n\nNot enough data for analysis.")
                return

            peak_hour_utc = hour_counts.most_common(1)[0][0]
            
            # Assumes peak usage is between 7 PM (19:00) and 10 PM (22:00) local time
            utc_offset_min = 19 - peak_hour_utc
            utc_offset_max = 22 - peak_hour_utc

            def format_offset(offset):
                # Wrap around 24h clock
                offset = (offset + 12) % 24 - 12
                return f"UTC{offset:+}"

            widget.update(f"[b]Estimated Timezone[/b]\n\nPeak activity around {peak_hour_utc:02d}:00 UTC suggests a timezone between {format_offset(utc_offset_min)} and {format_offset(utc_offset_max)}.")

        except Exception as e:
            widget.update(f"[b]Estimated Timezone[/b]\n\n[red]Error: {e}[/red]")

if __name__ == "__main__":
    app = ProfilerApp()
    app.run()