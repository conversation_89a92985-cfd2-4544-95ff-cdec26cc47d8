"""
Response Area Shell Widget - Replicates the original Posting app's ResponseArea component
"""

from textual.app import ComposeResult
from textual.containers import Vertical
from textual.reactive import Reactive, reactive
from textual.widgets import DataTable, Static, TabPane, TabbedContent, TextArea


class ResponseTextAreaShell(TextArea):
    """Shell version of the response text area"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.read_only = True


class ResponseHeadersTableShell(DataTable):
    """Shell version of the response headers table"""
    
    def on_mount(self) -> None:
        """Initialize the response headers table"""
        self.add_columns("Name", "Value")
        # Add some mock response headers
        self.add_rows([
            ("Content-Type", "application/json"),
            ("Content-Length", "1234"),
            ("Server", "nginx/1.18.0"),
            ("Cache-Control", "no-cache"),
            ("Date", "Mon, 25 Aug 2025 20:50:00 GMT"),
        ])


class ResponseCookiesShell(Vertical):
    """Shell version of the response cookies section"""
    
    def compose(self) -> ComposeResult:
        cookies_table = DataTable()
        cookies_table.add_columns("Name", "Value")
        cookies_table.add_rows([
            ("session_id", "abc123def456"),
            ("csrf_token", "xyz789uvw012"),
        ])
        yield cookies_table


class ResponseScriptsShell(Vertical):
    """Shell version of the response scripts output section"""
    
    def compose(self) -> ComposeResult:
        yield Static("Setup Script: ✓ Success")
        yield Static("Pre-request Script: ✓ Success") 
        yield Static("Post-response Script: ✓ Success")
        yield Static("Script Output:")
        yield TextArea(
            "Mock script output would appear here.\nConsole logs, print statements, etc.",
            read_only=True
        )


class ResponseTraceShell(Vertical):
    """Shell version of the response trace section"""
    
    def compose(self) -> ComposeResult:
        yield Static("Connection Trace:")
        yield Static("■ DNS Resolution: 12ms")
        yield Static("■ TCP Connect: 45ms")
        yield Static("■ TLS Handshake: 78ms") 
        yield Static("■ Request Headers: 5ms")
        yield Static("■ Request Body: 3ms")
        yield Static("■ Response Headers: 123ms")
        yield Static("■ Response Body: 67ms")
        yield Static("■ Total Time: 333ms")


class ResponseTabbedContentShell(TabbedContent):
    """Shell version of the response tabbed content"""
    pass


class ResponseAreaShell(Vertical):
    """Shell version of the response area with all tabs"""
    
    def compose(self) -> ComposeResult:
        with ResponseTabbedContentShell():
            with TabPane("Body", id="response-body-pane"):
                yield ResponseTextAreaShell(
                    """{
  "message": "Mock response from the shell interface",
  "status": "success",
  "data": {
    "items": [
      {"id": 1, "name": "Item 1"},
      {"id": 2, "name": "Item 2"},
      {"id": 3, "name": "Item 3"}
    ],
    "total": 3,
    "page": 1
  },
  "timestamp": "2025-08-25T20:50:00Z"
}""",
                    language="json"
                )
                
            with TabPane("Headers", id="response-headers-pane"):
                yield ResponseHeadersTableShell()
                
            with TabPane("Cookies", id="response-cookies-pane"):
                yield ResponseCookiesShell()
                
            with TabPane("Scripts", id="response-scripts-pane"):
                yield ResponseScriptsShell()
                
            with TabPane("Trace", id="response-trace-pane"):
                yield ResponseTraceShell()

    def on_mount(self) -> None:
        """Initialize the response area"""
        self.border_title = "Response [green] 200 OK [/]"
        self.border_subtitle = "1.23KB in 333.45[dim]ms[/]"
        self.add_class("section")
        self.add_class("success")  # Indicate successful response
