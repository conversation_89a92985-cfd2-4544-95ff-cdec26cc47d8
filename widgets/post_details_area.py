"""
Post Details Area Widget - Integrates BlueCLIent Post Details into Posting-style UI
Maps the old Post Details functionality to the new response area tabs:
- JSON tab: Post content and replies in JSON format
- Visual tab: Post content and replies in visual format
- Headers tab: Post metadata and author info
- Cookies tab: Post statistics and engagement
- Scripts tab: Action buttons (Like, Repost, Reply, Quote)
- Trace tab: Post thread and conversation flow
"""

import json
from datetime import datetime
from textual.app import ComposeResult
from textual.containers import Vertical, Horizontal
from textual.reactive import Reactive, reactive
from textual.widgets import DataTable, Static, TabPane, TabbedContent, TextArea, Button

# Import the original widgets we need
# from main_app.interactive_widgets import ComprehensivePostView


class PostBodyTabShell(Vertical):
    """Body tab - Shows post content in JSON format"""
    
    def __init__(self, client, post_data=None):
        super().__init__()
        self.client = client
        self.post_data = post_data
    
    def compose(self) -> ComposeResult:
        yield Vertical(id="post_content_area", classes="post-content-expanded")
    
    def on_mount(self) -> None:
        """Initialize with default content"""
        self.load_post_content()
    
    def set_post_data(self, post_data):
        """Update the post data and refresh content"""
        self.post_data = post_data
        self.load_post_content()
    
    def load_post_content(self):
        """Load post content in JSON format"""
        content_area = self.query_one("#post_content_area", Vertical)
        content_area.remove_children()
        
        if not self.post_data:
            content_area.mount(Static("No post selected", classes="empty-message"))
            return
        
        self.load_json_view(content_area)
    
    def load_json_view(self, content_area):
        """Load JSON view of post and replies"""
        try:
            post = self.post_data.post
            author = post.author
            record = post.record
            
            # Create JSON representation
            post_json = {
                "author": {
                    "display_name": getattr(author, 'display_name', 'Unknown'),
                    "handle": getattr(author, 'handle', 'unknown')
                },
                "content": getattr(record, 'text', 'No content'),
                "stats": {
                    "likes": getattr(post, 'like_count', 0),
                    "reposts": getattr(post, 'repost_count', 0),
                    "replies": getattr(post, 'reply_count', 0)
                },
                "created_at": getattr(record, 'created_at', 'Unknown'),
                "uri": post.uri,
                "replies": []
            }
            
            # Try to get replies
            try:
                thread = self.client.get_post_thread(uri=post.uri, depth=3)
                if thread and hasattr(thread, 'thread') and hasattr(thread.thread, 'replies') and thread.thread.replies:
                    post_json["replies"] = self._extract_replies_json(thread.thread.replies, max_depth=5)
            except Exception as reply_error:
                post_json["replies_error"] = f"Could not load replies: {str(reply_error)}"
            
            # Format as JSON
            json_text = json.dumps(post_json, indent=3, ensure_ascii=False)
            json_text = json_text.replace("[", r"\[").replace("]", r"\]")
            
            # Create text area
            json_widget = TextArea(json_text, read_only=True, classes="json-display-expanded")
            json_widget.show_line_numbers = False
            json_widget.cursor_blink = False
            content_area.mount(json_widget)
            
        except Exception as e:
            content_area.mount(Static(f"Error loading JSON view: {str(e)}", classes="error"))
    
    def _extract_replies_json(self, replies, max_depth=5, current_depth=0):
        """Extract replies data for JSON representation"""
        if current_depth >= max_depth:
            return []
            
        replies_data = []
        try:
            for reply_thread in replies:
                if hasattr(reply_thread, 'post'):
                    try:
                        reply_post = reply_thread.post
                        reply_author = reply_post.author
                        reply_record = reply_post.record
                        
                        content = getattr(reply_record, 'text', 'No content')
                        if isinstance(content, str):
                            content = content.replace('"', '\\"').replace('\n', '\\n')
                        
                        reply_data = {
                            "author": {
                                "display_name": getattr(reply_author, 'display_name', 'Unknown'),
                                "handle": getattr(reply_author, 'handle', 'unknown')
                            },
                            "content": content,
                            "stats": {
                                "likes": getattr(reply_post, 'like_count', 0),
                                "reposts": getattr(reply_post, 'repost_count', 0),
                                "replies": getattr(reply_post, 'reply_count', 0)
                            },
                            "created_at": getattr(reply_record, 'created_at', 'Unknown'),
                            "uri": reply_post.uri,
                            "replies": []
                        }
                        
                        if hasattr(reply_thread, 'replies') and reply_thread.replies and current_depth < max_depth - 1:
                            reply_data["replies"] = self._extract_replies_json(reply_thread.replies, max_depth, current_depth + 1)
                        
                        replies_data.append(reply_data)
                    except Exception:
                        continue
        except Exception:
            pass
        
        return replies_data


class PostVisualTabShell(Vertical):
    """Visual tab - Shows post content and replies in visual format"""
    
    def __init__(self, client, post_data=None):
        super().__init__()
        self.client = client
        self.post_data = post_data
    
    def compose(self) -> ComposeResult:
        yield Vertical(id="post_content_area", classes="post-content-expanded")
    
    def on_mount(self) -> None:
        """Initialize with default content"""
        self.load_post_content()
    
    def set_post_data(self, post_data):
        """Update the post data and refresh content"""
        self.post_data = post_data
        self.load_post_content()
    
    def load_post_content(self):
        """Load post content in visual format"""
        content_area = self.query_one("#post_content_area", Vertical)
        content_area.remove_children()
        
        if not self.post_data:
            content_area.mount(Static("No post selected", classes="empty-message"))
            return
        
        self.load_visual_view(content_area)
    
    def load_visual_view(self, content_area):
        """Load visual view of post and replies with 3-column layout"""
        try:
            post = self.post_data.post
            author = post.author
            record = post.record
            
            # Get post data with replies
            post_data = {
                "author": {
                    "display_name": getattr(author, 'display_name', 'Unknown'),
                    "handle": getattr(author, 'handle', 'unknown')
                },
                "content": getattr(record, 'text', 'No content'),
                "stats": {
                    "likes": getattr(post, 'like_count', 0),
                    "reposts": getattr(post, 'repost_count', 0),
                    "replies": getattr(post, 'reply_count', 0)
                },
                "replies": []
            }
            
            # Try to get replies
            try:
                thread = self.client.get_post_thread(uri=post.uri, depth=3)
                if thread and hasattr(thread, 'thread') and hasattr(thread.thread, 'replies') and thread.thread.replies:
                    post_data["replies"] = self._extract_replies_json(thread.thread.replies, max_depth=5)
            except Exception as reply_error:
                pass
            
            # Create visual container with proper styling for scrolling and full height
            visual_container = Vertical(classes="visual-post-container")
            content_area.mount(visual_container)
            
            # Add the main post
            self._add_visual_post(visual_container, post_data, 0)
            
            # Add replies recursively if they exist
            if post_data.get("replies"):
                self._add_visual_replies(visual_container, post_data["replies"], 1)
            
        except Exception as e:
            content_area.mount(Static(f"Error loading visual view: {str(e)}", classes="error"))
    
    def _add_visual_post(self, container, post_data, indent_level):
        """Add a single post in visual format with 3-column layout and indentation"""
        try:
            # Get color for username (same logic as feed)
            handle = post_data["author"]["handle"]
            colors = ["primary", "secondary", "accent", "warning", "error", "success"]
            color = colors[hash(handle) % len(colors)]
            
            # Create indentation prefix for replies
            indent_prefix = ""
            if indent_level > 0:
                indent_prefix = "│ " * (indent_level - 1) + "├─ "
            
            # Get post data
            author_name = post_data['author']['display_name']
            content = post_data["content"]
            likes = post_data['stats']['likes']
            replies = post_data['stats']['replies']
            reposts = post_data['stats']['reposts']
            
            # Create the three-column layout exactly like the original
            post_row = Horizontal(
                # Column 1: Author name with white text, colored background
                Static(f"{indent_prefix}[white]{author_name}[/]", classes=f"visual-author-box visual-author-{color}"),
                # Column 2: Content 
                Static(content, classes="visual-content-box"),
                # Column 3: Three separate stat boxes with icons
                Horizontal(
                    Static(f"❤️\n{likes}", classes="visual-stat-box"),
                    Static(f"🔄\n{reposts}", classes="visual-stat-box"), 
                    Static(f"💬\n{replies}", classes="visual-stat-box"),
                    classes="visual-stats-row"
                ),
                classes="visual-post-row"
            )
            
            container.mount(post_row)
            
        except Exception as e:
            # Add error info for debugging
            container.mount(Static(f"Error rendering post: {str(e)}", classes="error"))
    
    def _add_visual_replies(self, container, replies, indent_level):
        """Add replies recursively in visual format with proper indentation"""
        try:
            for reply in replies:
                self._add_visual_post(container, reply, indent_level)
                # Add nested replies
                if reply.get("replies"):
                    self._add_visual_replies(container, reply["replies"], indent_level + 1)
        except Exception as e:
            # Skip problematic replies
            pass
    
    def _extract_replies_json(self, replies, max_depth=5, current_depth=0):
        """Extract replies data for JSON representation"""
        if current_depth >= max_depth:
            return []
            
        replies_data = []
        try:
            for reply_thread in replies:
                if hasattr(reply_thread, 'post'):
                    try:
                        reply_post = reply_thread.post
                        reply_author = reply_post.author
                        reply_record = reply_post.record
                        
                        content = getattr(reply_record, 'text', 'No content')
                        if isinstance(content, str):
                            content = content.replace('"', '\\"').replace('\n', '\\n')
                        
                        reply_data = {
                            "author": {
                                "display_name": getattr(reply_author, 'display_name', 'Unknown'),
                                "handle": getattr(reply_author, 'handle', 'unknown')
                            },
                            "content": content,
                            "stats": {
                                "likes": getattr(reply_post, 'like_count', 0),
                                "reposts": getattr(reply_post, 'repost_count', 0),
                                "replies": getattr(reply_post, 'reply_count', 0)
                            },
                            "created_at": getattr(reply_record, 'created_at', 'Unknown'),
                            "uri": reply_post.uri,
                            "replies": []
                        }
                        
                        if hasattr(reply_thread, 'replies') and reply_thread.replies and current_depth < max_depth - 1:
                            reply_data["replies"] = self._extract_replies_json(reply_thread.replies, max_depth, current_depth + 1)
                        
                        replies_data.append(reply_data)
                    except Exception:
                        continue
        except Exception:
            pass
        
        return replies_data


class PostHeadersTabShell(Vertical):
    """Headers tab - Shows post metadata and author information"""
    
    def __init__(self, client, post_data=None):
        super().__init__()
        self.client = client
        self.post_data = post_data
    
    def compose(self) -> ComposeResult:
        headers_table = DataTable()
        headers_table.add_columns("Property", "Value")
        yield headers_table
    
    def on_mount(self) -> None:
        self.load_post_headers()
    
    def set_post_data(self, post_data):
        """Update the post data and refresh headers"""
        self.post_data = post_data
        self.load_post_headers()
    
    def load_post_headers(self):
        """Load post metadata as headers"""
        headers_table = self.query_one(DataTable)
        headers_table.clear()
        
        if not self.post_data:
            headers_table.add_row("Status", "No post selected")
            return
        
        try:
            post = self.post_data.post
            author = post.author
            record = post.record
            
            # Add post metadata as "headers"
            headers_table.add_row("Author", getattr(author, 'display_name', 'Unknown'))
            headers_table.add_row("Handle", f"@{getattr(author, 'handle', 'unknown')}")
            headers_table.add_row("Created", getattr(record, 'created_at', 'Unknown'))
            headers_table.add_row("URI", post.uri)
            headers_table.add_row("CID", getattr(post, 'cid', 'Unknown'))
            headers_table.add_row("Language", getattr(record, 'langs', ['Unknown'])[0] if getattr(record, 'langs', None) else 'Unknown')
            
            # Check if it's a reply
            if hasattr(record, 'reply') and record.reply:
                headers_table.add_row("Reply To", getattr(record.reply.parent, 'uri', 'Unknown'))
            
            # Add author metadata if available
            if hasattr(author, 'description') and author.description:
                headers_table.add_row("Author Bio", author.description[:100] + "..." if len(author.description) > 100 else author.description)
            
        except Exception as e:
            headers_table.add_row("Error", str(e))


class PostCookiesTabShell(Vertical):
    """Cookies tab - Shows post statistics and engagement metrics"""
    
    def __init__(self, client, post_data=None):
        super().__init__()
        self.client = client
        self.post_data = post_data
    
    def compose(self) -> ComposeResult:
        stats_table = DataTable()
        stats_table.add_columns("Metric", "Count")
        yield stats_table
    
    def on_mount(self) -> None:
        self.load_post_stats()
    
    def set_post_data(self, post_data):
        """Update the post data and refresh stats"""
        self.post_data = post_data
        self.load_post_stats()
    
    def load_post_stats(self):
        """Load post statistics as 'cookies'"""
        stats_table = self.query_one(DataTable)
        stats_table.clear()
        
        if not self.post_data:
            stats_table.add_row("Status", "No post selected")
            return
        
        try:
            post = self.post_data.post
            
            # Add engagement metrics
            stats_table.add_row("❤️ Likes", str(getattr(post, 'like_count', 0)))
            stats_table.add_row("🔄 Reposts", str(getattr(post, 'repost_count', 0)))
            stats_table.add_row("💬 Replies", str(getattr(post, 'reply_count', 0)))
            stats_table.add_row("📊 Total Engagement", str(
                getattr(post, 'like_count', 0) + 
                getattr(post, 'repost_count', 0) + 
                getattr(post, 'reply_count', 0)
            ))
            
            # Add author stats if available
            author = post.author
            if hasattr(author, 'followers_count'):
                stats_table.add_row("👥 Author Followers", str(author.followers_count))
            if hasattr(author, 'follows_count'):
                stats_table.add_row("➡️ Author Following", str(author.follows_count))
            if hasattr(author, 'posts_count'):
                stats_table.add_row("📝 Author Posts", str(author.posts_count))
            
        except Exception as e:
            stats_table.add_row("Error", str(e))


class PostScriptsTabShell(Vertical):
    """Scripts tab - Shows action buttons (Like, Repost, Reply, Quote)"""
    
    def __init__(self, client, post_data=None):
        super().__init__()
        self.client = client
        self.post_data = post_data
    
    def compose(self) -> ComposeResult:
        yield Static("Post Actions:", classes="actions-title")
        yield Horizontal(
            Button("❤️ Like", id="action_like", variant="default"),
            Button("🔄 Repost", id="action_repost", variant="default"),
            classes="action-row"
        )
        yield Horizontal(
            Button("💬 Reply", id="action_reply", variant="default"),
            Button("📝 Quote", id="action_quote", variant="default"),
            classes="action-row"
        )
        yield Static("", id="action_status", classes="action-status")
    
    def set_post_data(self, post_data):
        """Update the post data"""
        self.post_data = post_data
    
    def on_button_pressed(self, event: Button.Pressed) -> None:
        """Handle action button presses"""
        if not self.post_data:
            self.app.notify("No post selected", severity="warning")
            return
        
        if event.button.id == "action_like":
            self.handle_like()
        elif event.button.id == "action_repost":
            self.handle_repost()
        elif event.button.id == "action_reply":
            self.handle_reply()
        elif event.button.id == "action_quote":
            self.handle_quote()
    
    def handle_like(self):
        """Handle like action"""
        try:
            post = self.post_data.post
            result = self.client.like(post.uri, post.cid)
            if not isinstance(result, Exception):
                self.app.notify("❤️ Post liked!")
                self.update_status("✅ Liked successfully")
            else:
                self.app.notify(f"❌ Error: {str(result)}", severity="error")
                self.update_status(f"❌ Like failed: {str(result)}")
        except Exception as e:
            self.app.notify(f"❌ Error: {str(e)}", severity="error")
            self.update_status(f"❌ Error: {str(e)}")
    
    def handle_repost(self):
        """Handle repost action"""
        try:
            post = self.post_data.post
            result = self.client.repost(post.uri, post.cid)
            if not isinstance(result, Exception):
                self.app.notify("🔄 Post reposted!")
                self.update_status("✅ Reposted successfully")
            else:
                self.app.notify(f"❌ Error: {str(result)}", severity="error")
                self.update_status(f"❌ Repost failed: {str(result)}")
        except Exception as e:
            self.app.notify(f"❌ Error: {str(e)}", severity="error")
            self.update_status(f"❌ Error: {str(e)}")
    
    def handle_reply(self):
        """Handle reply action"""
        try:
            from main_app.screens import ReplyScreen
            self.app.push_screen(ReplyScreen(self.client, self.post_data))
            self.update_status("📝 Opening reply screen...")
        except Exception as e:
            self.app.notify(f"❌ Error: {str(e)}", severity="error")
            self.update_status(f"❌ Reply error: {str(e)}")
    
    def handle_quote(self):
        """Handle quote action"""
        try:
            from main_app.screens import QuoteScreen
            self.app.push_screen(QuoteScreen(self.client, self.post_data))
            self.update_status("📝 Opening quote screen...")
        except Exception as e:
            self.app.notify(f"❌ Error: {str(e)}", severity="error")
            self.update_status(f"❌ Quote error: {str(e)}")
    
    def update_status(self, message):
        """Update the action status display"""
        try:
            status_widget = self.query_one("#action_status", Static)
            status_widget.update(message)
        except:
            pass


class PostTraceTabShell(Vertical):
    """Trace tab - Shows post thread and conversation flow"""
    
    def __init__(self, client, post_data=None):
        super().__init__()
        self.client = client
        self.post_data = post_data
    
    def compose(self) -> ComposeResult:
        yield Static("Thread Trace:", classes="trace-title")
        yield Vertical(id="thread_trace_area", classes="trace-content")
    
    def on_mount(self) -> None:
        self.load_thread_trace()
    
    def set_post_data(self, post_data):
        """Update the post data and refresh trace"""
        self.post_data = post_data
        self.load_thread_trace()
    
    def load_thread_trace(self):
        """Load thread trace information"""
        trace_area = self.query_one("#thread_trace_area", Vertical)
        trace_area.remove_children()
        
        if not self.post_data:
            trace_area.mount(Static("No post selected", classes="empty-message"))
            return
        
        try:
            post = self.post_data.post
            record = post.record
            
            # Show thread position
            trace_area.mount(Static("■ Post URI: " + post.uri, classes="trace-step"))
            
            # Check if it's a reply
            if hasattr(record, 'reply') and record.reply:
                trace_area.mount(Static("■ Reply to: " + getattr(record.reply.parent, 'uri', 'Unknown'), classes="trace-step"))
                if hasattr(record.reply, 'root'):
                    trace_area.mount(Static("■ Thread root: " + getattr(record.reply.root, 'uri', 'Unknown'), classes="trace-step"))
            else:
                trace_area.mount(Static("■ Original post (not a reply)", classes="trace-step"))
            
            # Try to get thread info
            try:
                thread = self.client.get_post_thread(uri=post.uri, depth=1)
                if thread and hasattr(thread, 'thread'):
                    if hasattr(thread.thread, 'replies') and thread.thread.replies:
                        trace_area.mount(Static(f"■ Direct replies: {len(thread.thread.replies)}", classes="trace-step"))
                    else:
                        trace_area.mount(Static("■ No direct replies", classes="trace-step"))
                    
                    # Show parent if available
                    if hasattr(thread.thread, 'parent'):
                        trace_area.mount(Static("■ Has parent post", classes="trace-step"))
                else:
                    trace_area.mount(Static("■ Thread info unavailable", classes="trace-step"))
            except Exception as e:
                trace_area.mount(Static(f"■ Thread trace error: {str(e)}", classes="trace-step error"))
            
            # Show timing info
            created_at = getattr(record, 'created_at', 'Unknown')
            trace_area.mount(Static(f"■ Created: {created_at}", classes="trace-step"))
            
        except Exception as e:
            trace_area.mount(Static(f"Error loading trace: {str(e)}", classes="error"))


class PostDetailsAreaShell(Vertical):
    """Main Post Details Area - Replaces ResponseAreaShell with BlueCLIent functionality"""
    
    def __init__(self, client):
        super().__init__()
        self.client = client
        self.current_post_data = None
    
    def compose(self) -> ComposeResult:
        with TabbedContent():
            with TabPane("JSON", id="post-json-pane"):
                yield PostBodyTabShell(self.client)
                
            with TabPane("Visual", id="post-visual-pane"):
                yield PostVisualTabShell(self.client)
                
            with TabPane("Headers", id="post-headers-pane"):
                yield PostHeadersTabShell(self.client)
                
            with TabPane("Cookies", id="post-cookies-pane"):
                yield PostCookiesTabShell(self.client)
                
            with TabPane("Scripts", id="post-scripts-pane"):
                yield PostScriptsTabShell(self.client)
                
            with TabPane("Trace", id="post-trace-pane"):
                yield PostTraceTabShell(self.client)
    
    def on_mount(self) -> None:
        """Initialize the post details area"""
        self.border_title = "Post Details"
        self.border_subtitle = "Select a post to view details"
        self.add_class("section")
    
    def show_post_details(self, post_data):
        """Show details for a specific post"""
        self.current_post_data = post_data
        
        # Update border title
        try:
            author = post_data.post.author
            display_name = getattr(author, 'display_name', 'Unknown')
            self.border_title = f"Post Details - {display_name}"
            self.border_subtitle = f"@{getattr(author, 'handle', 'unknown')}"
        except:
            self.border_title = "Post Details"
            self.border_subtitle = "Post selected"
        
        # Update all tabs with the new post data
        try:
            json_tab = self.query_one("#post-json-pane", TabPane).children[0]
            json_tab.set_post_data(post_data)
        except:
            pass
        
        try:
            visual_tab = self.query_one("#post-visual-pane", TabPane).children[0]
            visual_tab.set_post_data(post_data)
        except:
            pass
        
        try:
            headers_tab = self.query_one("#post-headers-pane", TabPane).children[0]
            headers_tab.set_post_data(post_data)
        except:
            pass
        
        try:
            cookies_tab = self.query_one("#post-cookies-pane", TabPane).children[0]
            cookies_tab.set_post_data(post_data)
        except:
            pass
        
        try:
            scripts_tab = self.query_one("#post-scripts-pane", TabPane).children[0]
            scripts_tab.set_post_data(post_data)
        except:
            pass
        
        try:
            trace_tab = self.query_one("#post-trace-pane", TabPane).children[0]
            trace_tab.set_post_data(post_data)
        except:
            pass