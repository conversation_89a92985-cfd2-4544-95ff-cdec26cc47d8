"""
Collection Browser Shell Widget - Left panel with collection tree and feed
"""

from textual.app import ComposeResult
from textual.containers import Vertical, Horizontal
from textual.widgets import Static, Tree, ListView, ListItem, Button


class CollectionTreeShell(Tree):
    """Shell version of the collection tree"""
    
    def __init__(self, **kwargs):
        super().__init__("Collection Root", **kwargs)
    
    def on_mount(self) -> None:
        """Initialize the collection tree with mock data"""
        self.show_root = False
        
        # Add mock collection structure
        root = self.root.add("📁 My Collection")
        
        api_folder = root.add("📁 API Endpoints")
        api_folder.add("🔗 GET /users")
        api_folder.add("🔗 POST /users") 
        api_folder.add("🔗 PUT /users/{id}")
        api_folder.add("🔗 DELETE /users/{id}")
        
        auth_folder = root.add("📁 Authentication")
        auth_folder.add("🔗 POST /auth/login")
        auth_folder.add("🔗 POST /auth/logout")
        auth_folder.add("🔗 GET /auth/profile")
        
        tests_folder = root.add("📁 Tests")
        tests_folder.add("🔗 Health Check")
        tests_folder.add("🔗 API Status")
        
        # Expand the tree by default
        root.expand()
        api_folder.expand()


class FeedPostItem(ListItem):
    """A clean, minimal post item for the main feed list."""
    
    def __init__(self, client, post_data, parent_container=None) -> None:
        super().__init__()
        self.client = client
        self.post_data = post_data
        self.parent_container = parent_container
        
    def compose(self) -> ComposeResult:
        try:
            post = self.post_data.post
            author = post.author
            display_name = getattr(author, 'display_name', 'Unknown')
            handle = getattr(author, 'handle', 'unknown')
            
            record = post.record
            text = getattr(record, 'text', 'No content')
            
            # Process line breaks to prevent extra spacing
            text = text.replace('\n', ' ')
            
            # Truncate to first 30 characters as requested
            if len(text) > 30:
                text = text[:30] + "..."
            
            # Create colored username using theme palette colors
            colors = ["$primary", "$secondary", "$accent", "$warning", "$error", "$success"]
            color = colors[hash(handle) % len(colors)]
            
            # Post statistics
            likes = getattr(post, 'like_count', 0)
            reposts = getattr(post, 'repost_count', 0)
            replies = getattr(post, 'reply_count', 0)
            
            yield Horizontal(
                Static(f"[{color}]{display_name}[/]", classes="feed-username"),
                Static(f"{text}", classes="feed-text"),
                Static(f"❤️{likes} 🔄{reposts} 💬{replies}", classes="feed-stats"),
                classes="feed-item-row"
            )
            
        except Exception as e:
            yield Static(f"❌ Error: {str(e)}", classes="error")
    
    def on_click(self) -> None:
        """Handle click to show post details."""
        if self.parent_container:
            self.parent_container.show_post_details(self)


class CollectionBrowserShell(Vertical):
    """Shell version of the collection browser with feed"""
    
    def __init__(self, collection=None, client=None, **kwargs):
        super().__init__(**kwargs)
        self.collection = collection
        self.client = client

    def compose(self) -> ComposeResult:
        with Vertical() as vertical:
            vertical.border_title = "Collection"
            
            yield CollectionTreeShell(id="collection-tree")
            
            # Add feed section below existing content
         #   yield Static("📡 Modern Feed", classes="feed-section-title")
            yield ListView(id="main_feed_list", classes="main-feed")
            yield Button("🔄 Refresh Feed", id="refresh_feed", variant="default", classes="refresh-btn")

    def on_mount(self) -> None:
        """Initialize the collection browser"""
        self.border_title = "Collection"
        self.add_class("section")
        # Show by default
        self.display = True
        # Load feed if client is available
        if self.client:
            self.load_feed()
    
    def on_button_pressed(self, event) -> None:
        """Handle button presses for feed refresh"""
        if event.button.id == "refresh_feed":
            self.load_feed()
    
    def load_feed(self) -> None:
        """Load the main feed with clean post items."""
        if not self.client:
            return
            
        try:
            timeline = self.client.get_timeline()
            list_view = self.query_one("#main_feed_list", ListView)
            list_view.clear()
            
            if timeline and not isinstance(timeline, Exception):
                for post_data in timeline:
                    post_item = FeedPostItem(self.client, post_data, self)
                    list_view.append(post_item)
            else:
                list_view.append(ListItem(Static("No posts available", classes="empty-message")))
                
        except Exception as e:
            try:
                list_view = self.query_one("#main_feed_list", ListView)
                list_view.clear()
                list_view.append(ListItem(Static(f"Error loading feed: {str(e)}", classes="error")))
            except:
                pass
    
    def show_post_details(self, post_item):
        """Handle post click - send to PostDetailsAreaShell"""
        try:
            # Find the PostDetailsAreaShell in the screen and update it
            from widgets.post_details_area import PostDetailsAreaShell
            post_details_area = self.screen.query_one(PostDetailsAreaShell)
            post_details_area.show_post_details(post_item.post_data)
            
            # Also show notification
            author_name = getattr(post_item.post_data.post.author, 'display_name', 'Unknown')
            self.app.notify(f"📄 Showing details for post by {author_name}")
        except Exception as e:
            self.app.notify(f"Error showing post details: {str(e)}", severity="error")


class ProfilePostItem(ListItem):
    """A clean, minimal post item for the profile posts/replies list."""
    
    def __init__(self, client, post_data, parent_container=None) -> None:
        super().__init__()
        self.client = client
        # For profile content, the structure is slightly different
        # post_data might be a FeedViewPost or a like
        self.post_data = post_data
        self.parent_container = parent_container
        
    def compose(self) -> ComposeResult:
        try:
            # Handle both FeedViewPost and other structures
            if hasattr(self.post_data, 'post'):
                post = self.post_data.post
            else:
                post = self.post_data
                
            author = post.author
            display_name = getattr(author, 'display_name', 'Unknown')
            handle = getattr(author, 'handle', 'unknown')
            
            record = post.record
            text = getattr(record, 'text', 'No content')
            
            # Process line breaks to prevent extra spacing
            text = text.replace('\n', ' ')
            
            # Truncate to first 50 characters for better readability in profile
            if len(text) > 50:
                text = text[:50] + "..."
            
            # Create colored username using theme palette colors
            colors = ["$primary", "$secondary", "$accent", "$warning", "$error", "$success"]
            color = colors[hash(handle) % len(colors)]
            
            # Post statistics
            likes = getattr(post, 'like_count', 0)
            reposts = getattr(post, 'repost_count', 0)
            replies = getattr(post, 'reply_count', 0)
            
            yield Horizontal(
                Static(f"[{color}]{display_name}[/]", classes="feed-username"),
                Static(f"{text}", classes="feed-text"),
                Static(f"❤️{likes} 🔄{reposts} 💬{replies}", classes="feed-stats"),
                classes="feed-item-row"
            )
            
        except Exception as e:
            yield Static(f"❌ Error: {str(e)}", classes="error")
    
    def on_click(self) -> None:
        """Handle click to show post details."""
        if self.parent_container:
            # Create a compatible object for showing details
            self.parent_container.show_post_details(self)


class ProfileLikeItem(ListItem):
    """A clean, minimal item for the profile likes list."""
    
    def __init__(self, client, like_data, parent_container=None) -> None:
        super().__init__()
        self.client = client
        self.like_data = like_data
        self.parent_container = parent_container
        # Store the actual post data for details view
        if hasattr(like_data, 'post'):
            self.post_data = like_data.post
        else:
            self.post_data = like_data
        
    def compose(self) -> ComposeResult:
        try:
            # For likes, the structure is FeedViewPost which contains a post and a reason
            # The post is what was liked
            if hasattr(self.like_data, 'post'):
                post = self.like_data.post
            else:
                # Fallback to the like_data itself if it's the post
                post = self.like_data
                
            author = post.author
            display_name = getattr(author, 'display_name', 'Unknown')
            handle = getattr(author, 'handle', 'unknown')
            
            record = post.record
            text = getattr(record, 'text', 'No content')
            
            # Process line breaks to prevent extra spacing
            text = text.replace('\n', ' ')
            
            # Truncate to first 50 characters for better readability in profile
            if len(text) > 50:
                text = text[:50] + "..."
            
            # Create colored username using theme palette colors
            colors = ["$primary", "$secondary", "$accent", "$warning", "$error", "$success"]
            color = colors[hash(handle) % len(colors)]
            
            # Post statistics
            likes = getattr(post, 'like_count', 0)
            reposts = getattr(post, 'repost_count', 0)
            replies = getattr(post, 'reply_count', 0)
            
            yield Horizontal(
                Static("❤️", classes="like-indicator"),
                Static(f"[{color}]{display_name}[/]", classes="feed-username"),
                Static(f"{text}", classes="feed-text"),
                Static(f"❤️{likes} 🔄{reposts} 💬{replies}", classes="feed-stats"),
                classes="feed-item-row"
            )
            
        except Exception as e:
            yield Static(f"❌ Error: {str(e)}", classes="error")
    
    def on_click(self) -> None:
        """Handle click to show post details."""
        if self.parent_container:
            # Create a compatible object for showing details
            self.parent_container.show_post_details(self)
