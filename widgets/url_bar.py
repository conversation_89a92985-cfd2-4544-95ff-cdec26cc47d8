"""
URL Bar Shell Widget - Replicates the original Posting app's UrlBar component
"""

from textual.app import ComposeResult
from textual.containers import Horizontal, Vertical
from textual.reactive import Reactive, reactive
from textual.widgets import Button, Input, Label

from .method_selector import MethodSelectorShell


class UrlInputShell(Input):
    """Shell version of the URL input widget"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.placeholder = "Enter a URL or paste a curl command…"


class SendRequestButtonShell(Button, can_focus=False):
    """Shell version of the send request button"""
    
    def __init__(self):
        super().__init__("Send")


class UrlBarShell(Vertical):
    """Shell version of the URL bar - replicates the original UrlBar"""
    
    response_status_code: Reactive[int | None] = reactive(
        None, init=False, always_update=True
    )
    response_reason_phrase: Reactive[str | None] = reactive(
        None, init=False, always_update=True
    )

    def compose(self) -> ComposeResult:
        with Horizontal(id="main-row"):
            yield MethodSelectorShell(id="method-selector")
            yield UrlInputShell(
                placeholder="Enter a URL or paste a curl command…",
                id="url-input",
            )
            yield Label(id="response-status-code")
            yield Label(id="trace-markers")  
            yield SendRequestButtonShell()

        # Variable value preview bar (initially empty)
        yield Label(id="variable-value-bar")

    def watch_response_status_code(self, status_code: int | None) -> None:
        """Update status code display"""
        if status_code is None:
            return

        status_code_label = self.query_one("#response-status-code", Label)
        status_code_label.remove_class("-success", "-warning", "-error")
        
        if status_code < 300:
            status_code_label.add_class("-success")
        elif status_code < 400:
            status_code_label.add_class("-warning")
        else:
            status_code_label.add_class("-error")

        status_code_label.update(str(status_code))
        status_code_label.display = True

    def watch_response_reason_phrase(self, reason_phrase: str | None) -> None:
        """Update reason phrase tooltip"""
        if reason_phrase is None:
            return
        
        status_code_label = self.query_one("#response-status-code", Label)
        status_code_label.tooltip = reason_phrase

    def on_button_pressed(self, event: Button.Pressed) -> None:
        """Handle send button press"""
        if isinstance(event.button, SendRequestButtonShell):
            # Mock response for demo
            self.response_status_code = 200
            self.response_reason_phrase = "OK"
            
            # Show trace markers
            trace_markers = self.query_one("#trace-markers", Label)
            trace_markers.update("■■■■■■■")  # Mock trace markers
            trace_markers.add_class("has-events")
            
            self.app.notify("Mock HTTP request sent!")
