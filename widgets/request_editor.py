"""
Request Editor Shell Widget - Modern tabbed interface with BlueCLIent functionality
"""

import os
import json
from textual.app import ComposeResult
from textual.containers import Vertical, Horizontal
from textual.widgets import Select, TabPane, TabbedContent, TextArea, DataTable, Static, Button, ListView, ListItem

# Import the post item classes from collection browser
from .collection_browser import FeedPostItem, ProfilePostItem, ProfileLikeItem

class RequestTabbedContentShell(TabbedContent):
    """Shell version of the request tabbed content"""
    pass

class CreateTabContent(Vertical):
    """Create new post tab content"""
    
    def __init__(self, client, **kwargs):
        super().__init__(**kwargs)
        self.client = client
    
    def compose(self) -> ComposeResult:
       # yield Static("✏️ Create New Post", classes="tab-title")
        yield TextArea(id="new_post_text", classes="post-input-expanded")
        yield Horizontal(
            But<PERSON>("📤 Post", id="publish_post", variant="primary"),
            <PERSON><PERSON>("🗑️ Clear", id="clear_post", variant="default"),
            classes="post-actions-bottom"
        )

class ProfileTabContent(Vertical):
    """Profile information tab content"""
    
    def __init__(self, client, **kwargs):
        super().__init__(**kwargs)
        self.client = client
    
    def compose(self) -> ComposeResult:
        # yield Static("👤 Profile", classes="tab-title") 
        yield Static("Loading profile...", id="profile_header", classes="profile-info")

class NotificationsTabContent(Vertical):
    """Notifications tab content"""
    
    def __init__(self, client, **kwargs):
        super().__init__(**kwargs)
        self.client = client
    
    def compose(self) -> ComposeResult:
        # yield Static("🔔 Notifications", classes="tab-title")
        yield ListView(id="notifications_list", classes="notifications-list")
        yield Button("🔄 Refresh", id="refresh_notifications", variant="default", classes="refresh-btn")

class PostsTabContent(Vertical):
    """Posts tab content"""
    
    def __init__(self, client, **kwargs):
        super().__init__(**kwargs)
        self.client = client
        self.profile_page = 0
        self.profile_items_per_page = 100
        self.current_profile_handle = None
    
    def compose(self) -> ComposeResult:
        # yield Static("📝 Posts", classes="tab-title")
        yield ListView(id="posts_content_list", classes="profile-content-list")
        yield Button("🔄 Refresh", id="refresh_posts", variant="default", classes="refresh-btn")

class RepliesTabContent(Vertical):
    """Replies tab content"""
    
    def __init__(self, client, **kwargs):
        super().__init__(**kwargs)
        self.client = client
        self.profile_page = 0
        self.profile_items_per_page = 100
        self.current_profile_handle = None
    
    def compose(self) -> ComposeResult:
        # yield Static("💬 Replies", classes="tab-title")
        yield ListView(id="replies_content_list", classes="profile-content-list")
        yield Button("🔄 Refresh", id="refresh_replies", variant="default", classes="refresh-btn")

class LikesTabContent(Vertical):
    """Likes tab content"""
    
    def __init__(self, client, **kwargs):
        super().__init__(**kwargs)
        self.client = client
        self.profile_page = 0
        self.profile_items_per_page = 100
        self.current_profile_handle = None
    
    def compose(self) -> ComposeResult:
        # yield Static("❤️ Likes", classes="tab-title")
        yield ListView(id="likes_content_list", classes="profile-content-list")
        yield Button("🔄 Refresh", id="refresh_likes", variant="default", classes="refresh-btn")

class AccountManagerShell(Vertical):
    """Account switcher and manager - replaces the Body tab"""
    
    def __init__(self, client=None, **kwargs):
        super().__init__(**kwargs)
        self.client = client
        self.accounts = {}
        self.current_account = None
        self._updating_editor = False  # Flag to prevent infinite loops
        self._auto_save_timer = None  # Track pending auto-save operations
        
    def compose(self) -> ComposeResult:
        # Account selector dropdown
        # yield Static("🔄 Account Switcher", classes="tab-title")
        yield Select(
            [("No accounts loaded", "none")],
            value="none",
            id="account-selector"
        )
        
        yield Static("📝 Account Configuration (JSON):", classes="config-label")
        
        # JSON editor for account configuration
        yield TextArea(
            self._get_default_config(),
            language="json",
            id="account-config-editor"
        )
        
        yield Horizontal(
            Button("💾 Save & Update", id="save_accounts", variant="primary"),
            Button("🔄 Reload", id="reload_accounts", variant="default"),
            classes="account-actions"
        )
        
        yield Static("💡 Edit accounts above, then click Save & Update", classes="help-text")
    
    def on_mount(self) -> None:
        """Load existing accounts on mount"""
        self._updating_editor = True  # Disable auto-save during initial setup
        try:
            self.load_accounts_from_env()
            self.update_account_dropdown()
        finally:
            self._updating_editor = False  # Re-enable auto-save after setup
    
    def _get_default_config(self) -> str:
        """Get default account configuration template"""
        return """{
  "accounts": [
    {
      "BLUESKY_HANDLE": "your-handle.bsky.social",
      "BLUESKY_PASSWORD": "your-app-password"
    },
    {
      "BLUESKY_HANDLE1": "second-handle.bsky.social", 
      "BLUESKY_PASSWORD1": "second-app-password"
    },
    {
      "BLUESKY_HANDLE2": "third-handle.bsky.social",
      "BLUESKY_PASSWORD2": "third-app-password"
    }
  ]
}"""
    
    def load_accounts_from_env(self):
        """Load accounts from environment variables"""
        self.accounts = {}
        
        # Load primary account
        handle = os.getenv('BLUESKY_HANDLE')
        password = os.getenv('BLUESKY_PASSWORD')
        if handle and password:
            self.accounts['primary'] = {'handle': handle, 'password': password}
        
        # Load numbered accounts
        i = 1
        while True:
            handle = os.getenv(f'BLUESKY_HANDLE{i}')
            password = os.getenv(f'BLUESKY_PASSWORD{i}')
            if handle and password:
                self.accounts[f'account_{i}'] = {'handle': handle, 'password': password}
                i += 1
            else:
                break
        
        # Update the JSON editor with current accounts (only if not during mount)
        if hasattr(self, '_updating_editor'):
            self.update_config_editor()
    
    def update_config_editor(self):
        """Update the JSON editor with current account data"""
        try:
            self._updating_editor = True  # Prevent triggering auto-save
            
            config_data = {"accounts": []}
            
            for key, account in self.accounts.items():
                if key == 'primary':
                    config_data["accounts"].append({
                        "BLUESKY_HANDLE": account['handle'],
                        "BLUESKY_PASSWORD": account['password']
                    })
                else:
                    # Extract number from account_X
                    num = key.split('_')[1] if '_' in key else '1'
                    config_data["accounts"].append({
                        f"BLUESKY_HANDLE{num}": account['handle'],
                        f"BLUESKY_PASSWORD{num}": account['password']
                    })
            
            if not config_data["accounts"]:
                config_text = self._get_default_config()
            else:
                config_text = json.dumps(config_data, indent=2)
            
            text_area = self.query_one("#account-config-editor", TextArea)
            text_area.text = config_text
            
        except Exception as e:
            pass
        finally:
            self._updating_editor = False  # Re-enable auto-save
    
    def update_account_dropdown(self):
        """Update the account selector dropdown"""
        try:
            options = []
            
            if not self.accounts:
                options = [("No accounts configured", "none")]
            else:
                for key, account in self.accounts.items():
                    display_name = f"@{account['handle']}"
                    options.append((display_name, key))
            
            selector = self.query_one("#account-selector", Select)
            selector.set_options(options)
            
            # Set current account if available
            if self.client and hasattr(self.client, 'current_account') and self.client.current_account:
                selector.value = self.client.current_account
            elif options and options[0][1] != "none":
                selector.value = options[0][1]
                
        except Exception as e:
            pass
    
    def on_select_changed(self, event) -> None:
        """Handle dropdown selection changes - auto-switch accounts"""
        if event.control.id == "account-selector":
            self.switch_account()
    
    def on_button_pressed(self, event) -> None:
        """Handle button presses"""
        if event.button.id == "save_accounts":
            self.save_accounts_to_env()
        elif event.button.id == "reload_accounts":
            self.load_accounts_from_env()
            self.update_account_dropdown()
    
    def auto_save_accounts(self, *args):
        """Auto-save accounts when JSON editor changes"""
        self._auto_save_timer = None  # Clear the timer reference
        if not self._updating_editor:  # Double-check we're not in an update loop
            self.save_accounts_to_env()
    
    def save_accounts_to_env(self):
        """Save accounts from JSON editor to .env file"""
        try:
            text_area = self.query_one("#account-config-editor", TextArea)
            config_text = text_area.text.strip()
            
            if not config_text:
                self.app.notify("Please enter account configuration", severity="warning")
                return
            
            # Parse JSON
            try:
                config_data = json.loads(config_text)
            except json.JSONDecodeError as e:
                self.app.notify(f"Invalid JSON format: {str(e)}", severity="error")
                return
            
            if "accounts" not in config_data or not isinstance(config_data["accounts"], list):
                self.app.notify("JSON must contain 'accounts' array", severity="error")
                return
            
            # Read existing .env file
            env_path = ".env"
            env_lines = []
            
            if os.path.exists(env_path):
                with open(env_path, 'r') as f:
                    env_lines = f.readlines()
            
            # Remove existing BLUESKY_* lines
            env_lines = [line for line in env_lines if not line.strip().startswith('BLUESKY_')]
            
            # Add new account lines
            for i, account in enumerate(config_data["accounts"]):
                if i == 0:
                    # Primary account
                    handle_key = "BLUESKY_HANDLE"
                    password_key = "BLUESKY_PASSWORD"
                else:
                    # Numbered accounts
                    handle_key = f"BLUESKY_HANDLE{i}"
                    password_key = f"BLUESKY_PASSWORD{i}"
                
                # Find handle and password in account object
                handle = None
                password = None
                
                for key, value in account.items():
                    if 'HANDLE' in key.upper():
                        handle = value
                    elif 'PASSWORD' in key.upper():
                        password = value
                
                if handle and password:
                    env_lines.append(f"{handle_key}={handle}\n")
                    env_lines.append(f"{password_key}={password}\n")
            
            # Write back to .env file
            with open(env_path, 'w') as f:
                f.writelines(env_lines)
            
            # Reload accounts and update UI
            self.load_accounts_from_env()
            self.update_account_dropdown()
            
            self.app.notify(f"✅ Saved {len(config_data['accounts'])} accounts to .env")
            
        except Exception as e:
            self.app.notify(f"Error saving accounts: {str(e)}", severity="error")
    
    def switch_account(self):
        """Auto-switch to the selected account"""
        try:
            selector = self.query_one("#account-selector", Select)
            selected_account = selector.value
            
            if selected_account == "none" or not selected_account:
                return  # Don't show error for "none" selection
            
            if selected_account not in self.accounts:
                return  # Account might not be loaded yet
            
            if not self.client:
                return  # No client available
            
            # Don't switch if already on this account
            if hasattr(self.client, 'current_account') and self.client.current_account == selected_account:
                return
            
            # Switch account using client
            result = self.client.switch_account(selected_account)
            if result:
                self.app.notify(f"✅ Auto-switched to @{result.handle}")
                
                # Refresh all UI components
                try:
                    # Refresh feed
                    collection_browser = self.app.screen.query_one("CollectionBrowserShell")
                    if hasattr(collection_browser, 'load_feed'):
                        collection_browser.load_feed()
                    
                    # Refresh profile in request editor
                    request_editor = self.app.screen.query_one("RequestEditorShell")
                    if hasattr(request_editor, 'load_profile_header'):
                        request_editor.load_profile_header()
                        
                except Exception:
                    pass
                    
            else:
                self.app.notify("Failed to switch account", severity="warning")
                
        except Exception as e:
            # Silent fail for auto-switching to avoid spam
            pass

class RequestAuthShell(Vertical):
    """Shell version of the request auth section - UNCHANGED"""
    
    def compose(self) -> ComposeResult:
        yield Select(
            [
                ("No Auth", "none"),
                ("Basic Auth", "basic"),
                ("Bearer Token", "bearer"),
                ("API Key", "api-key"),
            ],
            value="none"
        )
        yield Static("Mock authentication settings would go here.")

class RequestEditorShell(Vertical):
    """Shell version of the request editor with BlueCLIent tabs"""
    
    def __init__(self, client=None, **kwargs):
        super().__init__(**kwargs)
        self.client = client
        self.current_tab = "create"
        self.profile_page = 0
        self.profile_items_per_page = 100
        self.current_profile_handle = None

    def compose(self) -> ComposeResult:
        with Vertical() as vertical:
            # vertical.border_title = "Request"
            
            with RequestTabbedContentShell():
                # BlueCLIent functionality tabs
                with TabPane("Create", id="create-pane"):
                    yield CreateTabContent(self.client)
                    
                with TabPane("Profile", id="profile-pane"):
                    yield ProfileTabContent(self.client)
                    
                with TabPane("Notifications", id="notifications-pane"):
                    yield NotificationsTabContent(self.client)
                    
                with TabPane("Posts", id="posts-pane"):
                    yield PostsTabContent(self.client)
                    
                with TabPane("Replies", id="replies-pane"):
                    yield RepliesTabContent(self.client)
                    
                with TabPane("Likes", id="likes-pane"):
                    yield LikesTabContent(self.client)
                
                # Account Manager tab (replaces Body tab)
                with TabPane("Body", id="body-pane"):
                    yield AccountManagerShell(self.client)
                    
                with TabPane("Auth", id="auth-pane"):
                    yield RequestAuthShell()

    def on_mount(self) -> None:
        """Initialize the request editor"""
        # self.border_title = "Request"
        self.add_class("section")
        
        # Load initial content if client is available
        if self.client:
            self.load_profile_header()
            self.load_notifications()

    def on_button_pressed(self, event) -> None:
        """Handle button presses"""
        if event.button.id == "publish_post":
            self.publish_new_post()
        elif event.button.id == "clear_post":
            self.clear_post_text()
        elif event.button.id == "refresh_notifications":
            self.load_notifications()
        elif event.button.id == "refresh_posts":
            self.load_profile_content("posts", "posts_content_list")
        elif event.button.id == "refresh_replies":
            self.load_profile_content("replies", "replies_content_list")
        elif event.button.id == "refresh_likes":
            self.load_profile_content("likes", "likes_content_list")
        # Account manager buttons are handled by AccountManagerShell itself

    def publish_new_post(self) -> None:
        """Publish a new post"""
        if not self.client:
            return
            
        try:
            text_area = self.query_one("#new_post_text", TextArea)
            text = text_area.text.strip()
            
            if not text or text == "What's happening?":
                self.app.notify("Please enter some text", severity="warning")
                return
                
            result = self.client.send_post(text)
            if not isinstance(result, Exception):
                self.app.notify("✅ Post published!")
                text_area.text = ""
                # Refresh feed in left panel
                collection_browser = self.app.screen.query_one("CollectionBrowserShell")
                if hasattr(collection_browser, 'load_feed'):
                    collection_browser.load_feed()
            else:
                self.app.notify(f"❌ Error: {str(result)}", severity="error")
                
        except Exception as e:
            self.app.notify(f"❌ Error: {str(e)}", severity="error")

    def clear_post_text(self) -> None:
        """Clear the post text area"""
        try:
            text_area = self.query_one("#new_post_text", TextArea)
            text_area.text = ""
            self.app.notify("Text cleared")
        except Exception as e:
            self.app.notify(f"Error clearing text: {str(e)}", severity="error")

    def load_profile_header(self) -> None:
        """Load user profile header information"""
        if not self.client:
            return
            
        try:
            profile = self.client.get_profile()
            if not isinstance(profile, Exception):
                profile_header = self.query_one("#profile_header", Static)
                profile_info = (
                    f"📛 {getattr(profile, 'display_name', 'N/A')}\n"
                    f"🏷️ @{getattr(profile, 'handle', 'N/A')}\n"
                    f"📝 Posts: {getattr(profile, 'posts_count', 'N/A')}\n"
                    f"👥 Followers: {getattr(profile, 'followers_count', 'N/A')}\n"
                    f"➡️ Following: {getattr(profile, 'follows_count', 'N/A')}\n"
                    f"📄 {getattr(profile, 'description', 'No description')}"
                )
                profile_header.update(profile_info)
                self.current_profile_handle = profile.handle
                
                # Auto-load posts content
                self.load_profile_content("posts", "posts_content_list")
            else:
                try:
                    profile_header = self.query_one("#profile_header", Static)
                    profile_header.update(f"Error loading profile: {str(profile)}")
                except:
                    pass
        except Exception as e:
            try:
                profile_header = self.query_one("#profile_header", Static)
                profile_header.update(f"Error loading profile: {str(e)}")
            except:
                pass

    def load_profile_content(self, content_type: str, list_view_id: str) -> None:
        """Load user profile content (posts, replies, or likes)"""
        if not self.client or not self.current_profile_handle:
            return
            
        try:
            list_view = self.query_one(f"#{list_view_id}", ListView)
            list_view.clear()
            
            if content_type == "posts":
                items = self.client.get_author_posts(self.current_profile_handle, limit=self.profile_items_per_page)
                item_class = ProfilePostItem
            elif content_type == "replies":
                items = self.client.get_author_replies(self.current_profile_handle, limit=self.profile_items_per_page)
                item_class = ProfilePostItem
            elif content_type == "likes":
                items = self.client.get_actor_likes(self.current_profile_handle, limit=self.profile_items_per_page)
                item_class = ProfileLikeItem
            else:
                items = []
                item_class = ProfilePostItem
            
            if not items:
                list_view.append(ListItem(Static(f"No {content_type} found", classes="empty-message")))
            else:
                # Add items to the list
                for item in items:
                    post_item = item_class(self.client, item, self)
                    list_view.append(post_item)
                    
        except Exception as e:
            try:
                list_view = self.query_one(f"#{list_view_id}", ListView)
                list_view.clear()
                list_view.append(ListItem(Static(f"Error loading {content_type}: {str(e)}", classes="error")))
            except:
                pass

    def load_notifications(self) -> None:
        """Load notifications"""
        if not self.client:
            return
            
        try:
            notifications = self.client.get_notifications()
            list_view = self.query_one("#notifications_list", ListView)
            list_view.clear()

            if isinstance(notifications, Exception):
                list_view.append(ListItem(Static(f"Error: {str(notifications)}", classes="error")))
                return

            if notifications:
                # Import NotificationWidget here to avoid circular imports
                try:
                    from main_app.widgets import NotificationWidget
                    
                    post_uris = []
                    for notification in notifications:
                        if notification.reason in ['like', 'repost']:
                            post_uris.append(notification.record.subject.uri)
                        elif notification.reason == 'reply':
                            post_uris.append(notification.record.reply.parent.uri)

                    if post_uris:
                        posts = self.client.get_posts(post_uris)
                        if isinstance(posts, Exception):
                            list_view.append(ListItem(Static(f"Error loading posts: {str(posts)}", classes="error")))
                            return
                        posts_map = {post.uri: post for post in posts}
                    else:
                        posts_map = {}

                    for notification in notifications:
                        list_view.append(NotificationWidget(notification, posts_map))
                except ImportError:
                    # Fallback to simple notification display
                    for notification in notifications:
                        reason = getattr(notification, 'reason', 'unknown')
                        author = getattr(notification, 'author', None)
                        if author:
                            author_name = getattr(author, 'display_name', 'Unknown')
                            list_view.append(ListItem(Static(f"{reason}: {author_name}", classes="notification")))
            else:
                list_view.append(ListItem(Static("No notifications", classes="empty-message")))

        except Exception as e:
            try:
                list_view = self.query_one("#notifications_list", ListView)
                list_view.clear()
                list_view.append(ListItem(Static(f"Error: {str(e)}", classes="error")))
            except:
                pass

    def show_post_details(self, post_item):
        """Handle post click from profile content"""
        # For now, just show a notification
        try:
            self.app.notify(f"Post clicked: {post_item.post_data.post.author.display_name}")
        except:
            pass
