"""
Method Selector Shell Widget - Replicates the original Posting app's MethodSelector component
"""

from textual.app import Co<PERSON>seResult
from textual.reactive import Reactive, reactive
from textual.widgets import Select


class MethodSelectorShell(Select):
    """Shell version of the HTTP method selector"""
    
    value: Reactive[str] = reactive("GET", init=False)
    
    def __init__(self, *args, **kwargs):
        # HTTP methods available in the original Posting app
        options = [
            ("GET", "GET"),
            ("POST", "POST"), 
            ("PUT", "PUT"),
            ("PATCH", "PATCH"),
            ("DELETE", "DELETE"),
            ("HEAD", "HEAD"),
            ("OPTIONS", "OPTIONS"),
            ("TRACE", "TRACE"),
        ]
        
        super().__init__(
            options, 
            value="GET",
            allow_blank=False,
            *args,
            **kwargs
        )

    def on_mount(self) -> None:
        """Initialize the method selector"""
        self.value = "GET"
