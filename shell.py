#!/usr/bin/env python3
"""
Shell - Modern UI for BlueCLIent
A clean, modern interface inspired by API clients with tabbed layout
and comprehensive functionality.
"""

import sys
from pathlib import Path
from typing import Any, Literal

from textual.app import App, ComposeResult
from textual import on
from textual.binding import Binding
from textual.containers import Horizontal, Vertical
from textual.screen import Screen
from textual.widgets import Button, Footer, Input, Label, Static, Tab, Tabs
from textual.widgets import TabPane, TabbedContent
from textual.command import (
    CommandPalette,
    CommandListItem,
    DiscoveryHit,
    Hit,
    Hits,
    Provider,
    SimpleCommand,
    SimpleProvider,
)
from textual.content import Content

from widgets.themes import BUILTIN_THEMES

# Import BlueskyClient
from bsky.client import BlueskyClient

# Import our custom widgets
from widgets.url_bar import UrlBarShell
from widgets.method_selector import MethodSelectorShell 
from widgets.collection_browser import CollectionBrowserShell
from widgets.request_editor import RequestEditorShell
from widgets.post_details_area import PostDetailsAreaShell


class AppHeaderShell(Horizontal):
    """The header of the app"""
    
    def compose(self) -> ComposeResult:
        yield Label("[b]BlueCLIent[/] [dim]Shell[/]", id="app-title")
        yield Label("user@host", id="app-user-host")


class AppBodyShell(Vertical):
    """The body of the app"""
    pass


class MainScreenShell(Screen[None]):
    """Main screen with modern tabbed interface"""
    
    def __init__(self, client):
        super().__init__()
        self.client = client
    
    BINDINGS = [
        Binding(
            "ctrl+j,alt+enter",
            "send_request",
            "Send",
            tooltip="Send the current request.",
            id="send-request",
        ),
        Binding(
            "ctrl+t",
            "change_method",
            "Method",
            tooltip="Focus the method selector.",
            id="focus-method",
        ),
        Binding(
            "ctrl+o",
            "toggle_jump_mode",
            description="Jump",
            tooltip="Activate jump mode to quickly move focus between widgets.",
            id="jump",
        ),
        Binding(
            "ctrl+l",
            "app.focus('url-input')",
            "Focus URL input",
            show=False,
            tooltip="Focus the URL input.",
            id="focus-url",
        ),
        Binding(
            "ctrl+s",
            "save_request",
            "Save",
            tooltip="Save the current request.",
            id="save-request",
        ),
        Binding(
            "ctrl+n",
            "new_request",
            "New",
            tooltip="Create a new request.",
            id="new-request",
        ),
        Binding(
            "ctrl+m",
            "toggle_expanded",
            "Expand section",
            show=False,
            tooltip="Expand or shrink the section.",
            id="expand-section",
        ),
        Binding(
            "ctrl+h",
            "toggle_collection_browser",
            "Toggle collection browser",
            show=False,
            tooltip="Toggle the collection browser.",
            id="toggle-collection",
        ),
        Binding(
            "ctrl+P,ctrl+shift+p",
            "open_request_search_palette",
            "Search requests",
            show=True,
            tooltip="Search for a request by name.",
            id="search-requests",
        ),
    ]

    def compose(self) -> ComposeResult:
        yield AppHeaderShell()
        yield UrlBarShell()
        
        # Main content area with horizontal layout
        with Horizontal():
            # Left panel - Collection browser with feed (always visible)
            # PASS THE CLIENT PROPERLY LIKE THE WORKING APP
            yield CollectionBrowserShell(collection=None, client=self.client)
            
            # Right panel - Request editor and Response area stacked vertically
            with Vertical():
                yield RequestEditorShell(client=self.client)
                yield PostDetailsAreaShell(self.client)

        # Footer
        footer = Footer(show_command_palette=False)
        footer.compact = False
        yield footer

    def on_mount(self) -> None:
        """Initialize the screen"""
        # Set the initial focus to URL input
        try:
            self.set_focus(self.query_one("#url-input", Input))
        except:
            pass

    def action_send_request(self) -> None:
        """Mock send request action"""
        self.app.notify("Mock request sent!")

    def action_change_method(self) -> None:
        """Focus the method selector"""
        try:
            method_selector = self.query_one("#method-selector")
            method_selector.focus()
        except:
            pass

    def action_toggle_jump_mode(self) -> None:
        """Mock toggle jump mode"""
        self.app.notify("Jump mode toggled!")

    def action_save_request(self) -> None:
        """Mock save request"""
        self.app.notify("Mock request saved!")

    def action_new_request(self) -> None:
        """Mock new request"""
        self.app.notify("Mock new request created!")

    def action_toggle_expanded(self) -> None:
        """Mock toggle expanded section"""
        self.app.notify("Mock section expanded/collapsed!")

    def action_toggle_collection_browser(self) -> None:
        """Toggle the collection browser visibility"""
        try:
            collection_browser = self.query_one(CollectionBrowserShell)
            collection_browser.display = not collection_browser.display
            self.app.notify(f"Collection browser {'shown' if collection_browser.display else 'hidden'}!")
        except:
            pass

    def action_open_request_search_palette(self) -> None:
        """Mock open request search palette"""
        self.app.notify("Mock request search palette opened!")


class ShellProvider(Provider):
    async def discover(self) -> Hits:
        """Handle a request for the discovery commands for this provider."""
        yield DiscoveryHit(
            "Change theme",
            self.app.action_change_theme,
            help="Preview a theme for the current session",
        )

    async def search(self, query: str) -> Hits:
        """Handle a request to search for commands that match the query."""
        matcher = self.matcher(query)
        if matcher.match("theme"):
            yield Hit(
                matcher.match("theme"),
                matcher.highlight("Change theme"),
                self.app.action_change_theme,
                help="Preview a theme for the current session",
            )


class Shell(App[None]):
    """Main Shell application class"""
    
    CSS_PATH = Path(__file__).parent / "shell.scss"
    BINDINGS = [
        Binding(
            "ctrl+p",
            "command_palette",
            description="Commands",
            tooltip="Open the command palette to search and run commands.",
            id="commands",
        ),
        Binding(
            "ctrl+q",
            "app.quit",
            description="Quit",
            tooltip="Quit the application.",
            priority=True,
            id="quit",
        ),
        Binding(
            "f1,ctrl+question_mark,ctrl+shift+slash",
            "help",
            "Help",
            tooltip="Open the help dialog.",
            id="help",
        ),
        Binding("f8", "save_screenshot", "Save screenshot.", show=False),
    ]

    COMMANDS = {ShellProvider}

    def __init__(self):
        super().__init__()
        # Register the themes
        for theme in BUILTIN_THEMES.values():
            self.register_theme(theme)
        self.theme = "galaxy"
        
        # Initialize BlueskyClient EXACTLY like the working app
        self.client = BlueskyClient()

    def compose(self) -> ComposeResult:
        """Create the content of the app."""
        footer = Footer()
        footer.compact = True
        yield footer

    def on_mount(self) -> None:
        """Set up the app on mount - EXACTLY like the working app"""
        # Try automatic login with first available account
        accounts = self.client.get_account_list()
        if accounts:
            # Try to login with the first account
            account_key, handle = accounts[0]
            result = self.client.switch_account(account_key)
            if result:
                self.notify(f"Auto-logged in as {result.display_name}")
                # Push our main screen with the client
                self.push_screen(MainScreenShell(self.client))
            else:
                self.notify(f"Auto-login failed for {handle}", severity="warning")
                # Should push login screen, but for now just push main screen
                self.push_screen(MainScreenShell(self.client))
        else:
            self.notify("No accounts found", severity="warning")
            # Should push login screen, but for now just push main screen  
            self.push_screen(MainScreenShell(self.client))

    def get_default_screen(self) -> MainScreenShell:
        return MainScreenShell(self.client)

    def action_help(self) -> None:
        """Mock help action"""
        self.notify("This is the BlueCLIent Shell - a modern UI!")

    def action_save_screenshot(self) -> None:
        """Save screenshot functionality"""
        filename = self.save_screenshot()
        self.notify(f"Screenshot saved as {filename}")

    def action_change_theme(self) -> None:
        """Change the theme."""
        self.search_commands(
            [
                SimpleCommand(
                    name=theme.name,
                    callback=lambda theme=theme: self.set_theme(theme.name),
                    help_text=f"Set the theme to {theme.name}",
                )
                for theme in self.available_themes.values()
            ],
            placeholder="Search for a theme...",
            palette_id="theme-search-command-palette",
        )

    def set_theme(self, theme_name: str) -> None:
        self.theme = theme_name

    @on(CommandPalette.Opened)
    def palette_opened(self) -> None:
        self._original_theme = self.theme

    @on(CommandPalette.OptionHighlighted)
    def palette_option_highlighted(
        self, event: CommandPalette.OptionHighlighted
    ) -> None:
        prompt = event.highlighted_event.option.prompt
        themes = self.available_themes.keys()
        if isinstance(prompt, Content):
            candidate = prompt.plain
            if candidate in themes:
                self.theme = candidate
            else:
                self.theme = self._original_theme
            self.call_next(self.screen._update_styles)

    @on(CommandPalette.Closed)
    def palette_closed(self, event: CommandPalette.Closed) -> None:
        if not event.option_selected:
            self.theme = self._original_theme

    def search_commands(
        self,
        commands: list[CommandListItem],
        placeholder: str = "Search for commands…",
        palette_id: str = "",
    ) -> None:
        palette = CommandPalette(
            providers=[SimpleProvider(self.screen, commands)],
            placeholder=placeholder,
            id=palette_id or None,
        )
        self.push_screen(palette)


def main():
    """Entry point for the Shell application"""
    app = Shell()
    app.run()


if __name__ == "__main__":
    main()
