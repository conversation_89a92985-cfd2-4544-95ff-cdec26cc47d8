/* 
Posting Shell SCSS - Adapted from the original Posting app styling
This file replicates the exact visual appearance of the original Posting TUI
*/

$empty-hatch: right $surface-lighten-1 70%;

* {
  scrollbar-color: $primary 10%;
  scrollbar-color-hover: $primary 80%;
  scrollbar-color-active: $primary;
  scrollbar-background: $surface-darken-1;
  scrollbar-background-hover: $surface-darken-1;
  scrollbar-background-active: $surface-darken-1;
  scrollbar-size-vertical: 1;

  &:focus {
    scrollbar-color: $primary 50%;
  }
}

PostingShell {
  background: $background;
}

Screen {
  background: $background;
}

ModalScreen {
  background: black 30%;
}

Footer {
  padding-left: 2;
}

.section {
  border: round $accent 40%;
  border-title-color: $text-accent 50%;
  border-title-align: right;

  &:focus-within {
    border: round $accent 100%;
    border-title-color: $foreground;
    border-title-style: b;
  }
}

.hidden {
  display: none;
}

.w-auto {
  width: auto;
}

.dock-right {
  dock: right;
}

.dock-left {
  dock: left;
}

/* App Header Styling */
AppHeaderShell {
  color: $text-primary;
  padding: 1 3;
  height: auto;

  & > #app-user-host {
    dock: right;
    color: $text-muted;
  }
}

/* App Body Layout */
AppBodyShell {
  padding: 0 2;
  layout: vertical;
}

/* URL Bar Styling */
UrlBarShell {
  height: 1;
  padding: 0 3 0 3;

  & #main-row {
    height: 1;
  }

  & #response-status-code {
    display: none;

    &.-success {
      color: $text-success;
      background: $success-muted;
      display: block;
      padding: 0 1;
    }

    &.-warning {
      color: $text-warning;
      background: $warning-muted;
      display: block;
      padding: 0 1;
    }

    &.-error {
      color: $text-error;
      background: $error-muted;
      display: block;
      padding: 0 1;
    }
  }

  & #trace-markers {
    color: $text-muted;

    &.has-events {
      color: $primary;
    }
  }

  & #variable-value-bar {
    color: $text-muted;
    height: auto;
    padding: 0 1;
    min-height: 0;
  }
}

/* Method Selector */
MethodSelectorShell {
  width: 8;
  min-width: 8;
}

/* Input Styling */
Input {
  border: none;
  width: 1fr;

  &:focus {
    border: none;
    padding: 0 1;
  }

  &.error {
    border-left: thick $error;
  }
}

/* Button Styling */
Button {
  padding: 0 0;
}

SendRequestButtonShell {
  width: 2;
  min-width: 2;
}

/* Data Table Styling */
DataTable {
  height: auto;
  width: 1fr;
  padding: 0 1;

  &:focus {
    width: 1fr;
    padding: 0;
    border-left: inner $accent;
  }
}

/* Select Widget */
Select {
  width: 1fr;
}

/* TextArea Styling */
TextArea {
  border: none;
  padding: 1;

  &:focus {
    border-left: inner $accent;
    padding-left: 0;
  }
}

/* Request Editor Styling */
RequestEditorShell {
  & RequestTabbedContentShell {
    height: 1fr;
  }

  & #request-body-type-select {
    margin-bottom: 1;
  }

  & #text-body-editor {
    height: 1fr;
    min-height: 10;
  }
}

/* Response Area Styling */
ResponseAreaShell {
  border-subtitle-color: $text-muted;

  & ResponseTextAreaShell.empty {
    display: none;
  }

  &.success .border-title-status {
    color: $text-success;
    background: $success-muted;
  }

  &.warning .border-title-status {
    color: $text-warning;
    background: $warning-muted;
  }

  &.error .border-title-status {
    color: $text-error;
    background: $error-muted;
  }

  & ResponseTabbedContentShell {
    height: 1fr;
  }
}

/* Collection Browser Styling */
CollectionBrowserShell {
  background: $surface 50%;
  width: 35%;
  dock: left;

  &:focus {
    outline: vkey $accent;
  }

  & CollectionTreeShell {
    height: 0.5fr;
  }
}

/* Tree Styling */
Tree {
  scrollbar-size-vertical: 1;
}

/* Tabbed Content */
TabbedContent {
  height: 1fr;
}

TabPane {
  height: 1fr;
  padding: 1;
}

Tabs {
  background: $surface;

  & Tab {
    padding: 0 2;

    &.-active {
      background: $accent-muted;
      color: $text-accent;
    }

    &:hover {
      background: $surface-lighten-1;
    }
  }
}

/* Static text styling for mock data */
Static {
  padding: 0;
  margin: 0;

  &.mock-data {
    color: $text-muted;
    text-style: italic;
  }
}

/* Note: Textual CSS doesn't support media queries like standard CSS
   Responsive behavior should be handled in Python code */

/* Feed item styling - copied from working app */
.main-feed {
  height: 1fr;
  border: none;
  background: transparent;
  width: auto;
}

.main-feed ListItem {
  border: none;
  background: transparent;
  padding: 0;
  margin: 0;
  height: auto;
}

.main-feed ListItem:hover {
  background: $surface;
}

.feed-item-row {
  layout: horizontal;
  height: auto;
  padding: 0;
  margin: 0;
  background: transparent;
}

.feed-username {
  width: 15;
  text-style: bold;
  margin: 0;
  padding: 0 1 0 0;
  text-align: left;
}

.feed-text {
  width: 25;
  margin: 0;
  padding: 0 1 0 0;
  overflow: hidden;
  text-overflow: ellipsis;
}

.feed-stats {
  width: 12;
  text-align: left;
  color: $text-muted;
  margin: 0;
  padding: 0;
}

.feed-section-title {
  text-style: bold;
  color: $primary;
  margin: 1 0;
  text-align: center;
}

.refresh-btn {
  margin: 0;
  width: 100%;
  height: 2;
}

.empty-message {
  color: $text-muted;
  text-style: italic;
  text-align: center;
  padding: 2;
}

.error {
  color: $error;
  text-style: bold;
  padding: 1;
}

/* Visual Post Presentation Styles - 3-column layout with indentation */
.visual-post-container {
  height: 100%;
  overflow-y: auto;
  padding: 1;
}

.visual-post-container-expanded {
  height: 100%;
  overflow-y: auto;
  padding: 1;
}

.visual-post-row {
  layout: horizontal;
  height: auto;
  min-height: 3;
  margin-bottom: 0;
  padding: 0;
  background: transparent;
}

.visual-author-box {
  width: 20;
  min-width: 20;
  max-width: 20;
  text-style: bold;
  margin: 0;
  padding: 1;
  text-align: center;
  content-align: center middle;
  border: solid $primary;
}

.visual-content-box {
  width: 1fr;
  margin: 0;
  padding: 1;
  background: $surface;
  border: solid $primary;
  text-wrap: wrap;
  overflow: hidden;
  height: auto;
}

.visual-stats-row {
  layout: horizontal;
  width: 12;
  min-width: 12;
  max-width: 12;
  margin: 0;
  padding: 0;
}

.visual-stat-box {
  width: 4;
  min-width: 4;
  max-width: 4;
  height: auto;
  min-height: 3;
  background: $surface;
  border: solid $primary;
  padding: 0;
  margin: 0;
  text-align: center;
  content-align: center middle;
  color: $text;
  text-style: bold;
}

/* JSON display styling */
.json-display-expanded {
  height: 100%;
  overflow-y: auto;
  padding: 1;
  border: none !important;
}

/* Colored backgrounds for author boxes */
.visual-author-primary {
  background: $primary;
}

.visual-author-secondary {
  background: $secondary;
}

.visual-author-accent {
  background: $accent;
}

.visual-author-warning {
  background: $warning;
}

.visual-author-error {
  background: $error;
}

.visual-author-success {
  background: $success;
}

/* Account Manager Styles */
.tab-title {
  text-style: bold;
  color: $primary;
  margin: 0 0 1 0;
  text-align: center;
}

.account-actions {
  layout: horizontal;
  height: 3;
  margin: 1 0;
}

.account-actions Button {
  margin: 0 0 0 1;
  width: 0.5fr;
}

.config-label {
  text-style: bold;
  color: $text;
  margin: 1 0 0 0;
}

.help-text {
  color: $text-muted;
  text-style: italic;
  margin: 1 0 0 0;
  text-align: center;
}

#account-selector {
  margin: 0 0 1 0;
  height: 3;
}

#account-config-editor {
  height: 1fr;
  margin: 1 0;
  border: solid $primary;
}

/* Expanded Post Input Styles */
.post-input-expanded {
  height: 1fr;
  margin: 0 0;
  border: solid $primary;
}

.post-actions-bottom {
  layout: horizontal;
  height: 1;
  align-horizontal: right;
  margin-top: 1;
}

#publish_post,
#clear_post,
#save_accounts,
#reload_accounts {
  height: 1;
  padding: 0 1;
  border: none;
  padding: 0 0;
  margin: 0 0 0 1;
}

#publish_post,
#save_accounts {
  width: 1;
  background: $accent-muted;
  color: $text-accent;
  text-style: b;
}

#publish_post:hover,
#save_accounts:hover {
  background-tint: $text-accent 10%;
}

#clear_post,
#reload_accounts {
  width: 1;
  background: $surface;
  color: $text-muted;
}

#clear_post:hover {
  background: $surface-lighten-1;
}
