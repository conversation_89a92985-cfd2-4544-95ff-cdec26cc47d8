# Posting Shell - UI Replica

This is a visual shell that replicates the exact layout and styling of the original [Posting](https://github.com/darrenburns/posting) TUI application without the core HTTP functionality.

## Features

✅ **Exact UI Replica**: Replicates all visual elements, buttons, tabs, and layout from the original Posting app  
✅ **Interactive Interface**: All widgets are functional for UI demonstration purposes  
✅ **Complete Styling**: Uses adapted SCSS from the original app for identical appearance  
✅ **Mock Data**: Shows realistic mock data in all sections  
✅ **Keyboard Shortcuts**: All original keybindings are preserved  

## Components Replicated

### Main Interface
- **Header**: App title and user/host information
- **URL Bar**: Method selector, URL input, Send button, status code display, trace markers
- **Request Editor**: Tabbed interface with Headers, Body, Query, Auth, Info, Scripts, Options
- **Response Area**: Tabbed interface with Body, Headers, Cookies, Scripts, Trace  
- **Collection Browser**: Tree view of mock requests (toggleable with Ctrl+H)
- **Footer**: Status bar with keybindings

### Request Editor Tabs
- **Headers**: Mock HTTP headers table
- **Body**: JSON text editor with syntax highlighting
- **Query**: Query parameters table
- **Auth**: Authentication options selector
- **Info**: Request metadata (name, description, collection)
- **Scripts**: Pre/post request script settings
- **Options**: Request options (SSL, redirects, timeout, etc.)

### Response Area Tabs
- **Body**: JSON response with syntax highlighting
- **Headers**: Response headers table  
- **Cookies**: Response cookies table
- **Scripts**: Script execution output
- **Trace**: Connection trace information

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Run the shell:
```bash
python posting_shell.py
```

## Keyboard Shortcuts

All original Posting keyboard shortcuts are preserved:

- `Ctrl+J` / `Alt+Enter` - Send request (mock)
- `Ctrl+T` - Focus method selector
- `Ctrl+L` - Focus URL input
- `Ctrl+S` - Save request (mock)
- `Ctrl+N` - New request (mock)
- `Ctrl+H` - Toggle collection browser
- `Ctrl+O` - Jump mode (mock)
- `Ctrl+P` - Command palette
- `Ctrl+Shift+P` - Search requests (mock)
- `Ctrl+M` - Toggle expand section (mock)
- `F1` - Help
- `Ctrl+C` - Quit

## Purpose

This shell is intended for:
- **UI/UX Testing**: Test interface layouts and interactions
- **Design Validation**: Ensure visual consistency with the original
- **Development Reference**: Understand the original app's structure
- **Training/Demo**: Show the interface without backend dependencies

## File Structure

```
shell/
├── posting_shell.py          # Main application
├── posting_shell.scss        # Styling (adapted from original)
├── requirements.txt          # Dependencies
├── widgets/                  # UI components
│   ├── __init__.py
│   ├── url_bar.py           # URL bar and method selector
│   ├── method_selector.py   # HTTP method dropdown
│   ├── request_editor.py    # Request tabs and forms
│   ├── response_area.py     # Response tabs and content
│   └── collection_browser.py # Collection tree and preview
└── README.md                # This file
```

## Notes

- This is a **visual replica only** - no actual HTTP requests are sent
- All data shown is mock/sample data for demonstration
- The UI layout and styling match the original Posting app exactly
- All interactive elements provide visual feedback but don't perform real operations
